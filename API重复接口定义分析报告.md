# API 重复接口定义分析报告

## 概述

本文档记录了 `src/api` 目录下发现的重复接口定义，用于后续优化和重构参考。

## 重复接口列表

### 1. 发送验证码接口 (sendCodeMsg)

**重复位置：**

- `src/api/user.ts` (第 96-101 行)
- `src/api/setPhoneNumber.ts` (第 4-9 行)

**接口详情：**

```typescript
// user.ts 版本
export const sendCodeMsg = (data = {}) => {
  return http.post("/common/api/sms/send/short/msg", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

// setPhoneNumber.ts 版本
export const sendCodeMsg = (data = {}) => {
  return http.post("/common/api/sms/send/short/msg", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
```

**差异分析：** 两个版本完全相同

### 2. 更新手机号接口 (changePhone)

**重复位置：**

- `src/api/user.ts` (第 104-108 行)
- `src/api/setPhoneNumber.ts` (第 19-24 行)

**接口详情：**

```typescript
// user.ts 版本
export const changePhone = (data = {}) => {
  return http.post("/common/api/update/bind/phone", data, {
    type: "formData",
  });
};

// setPhoneNumber.ts 版本
export const changePhone = (data = {}) => {
  return http.post("/common/api/update/bind/phone", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
```

**差异分析：** setPhoneNumber.ts 版本多了 `transformResult: (res) => res.data` 配置

### 3. 绑定手机号接口 (bindPhone)

**重复位置：**

- `src/api/user.ts` (第 110-114 行)
- `src/api/setPhoneNumber.ts` (第 26-31 行)

**接口详情：**

```typescript
// user.ts 版本
export const bindPhone = (data = {}) => {
  return http.post("/common/api/player/add-bind", data, {
    type: "formData",
  });
};

// setPhoneNumber.ts 版本
export const bindPhone = (data = {}) => {
  return http.post("/common/api/player/add-bind", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
```

**差异分析：** setPhoneNumber.ts 版本多了 `transformResult: (res) => res.data` 配置

### 4. 验证码校验接口 (verifyCode)

**重复位置：**

- `src/api/user.ts` (第 175-188 行)
- `src/api/setPhoneNumber.ts` (第 33-38 行)

**接口详情：**

```typescript
// user.ts 版本
export const verifyCode = (data = {}) => {
  return http.post(
    "/common/api/sms/verify/short/msg/code",
    {
      telephoneCode: "+63",
      type: "3",
      ...data,
    },
    {
      type: "formData",
      transformResult: (res) => res.data,
    }
  );
};

// setPhoneNumber.ts 版本
export const verifyCode = (data = {}) => {
  return http.post("/common/api/sms/verify/short/msg/code", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
```

**差异分析：** user.ts 版本预设了默认参数 `telephoneCode: "+63"` 和 `type: "3"`

### 5. 活动奖励列表接口 (getBonusList/getActivityBonusList)

**重复位置：**

- `src/api/games.ts` (第 88-90 行) - 函数名：getBonusList
- `src/api/activity.ts` (第 14-16 行) - 函数名：getActivityBonusList

**接口详情：**

```typescript
// games.ts 版本
export const getBonusList = (data: requestData) => {
  return http.post("/avt/api/activity/bonus_list", data);
};

// activity.ts 版本
export const getActivityBonusList = (data = {}) => {
  return http.post("/avt/api/activity/bonus_list", data, { type: "formData" });
};
```

**差异分析：**

- 函数名不同 (getBonusList vs getActivityBonusList)
- games.ts 版本有类型约束 `requestData`
- activity.ts 版本有 `type: "formData"` 配置

### 6. Jili 排行榜接口 (getRankjili/rankSolt)

**重复位置：**

- `src/api/activity.ts` (第 9-11 行) - 函数名：getRankjili
- `src/api/promos.ts` (第 4-6 行) - 函数名：rankSolt

**接口详情：**

```typescript
// activity.ts 版本
export const getRankjili = (data = {}) => {
  return http.post("/avt/api/rank/slot", data, { type: "formData" });
};

// promos.ts 版本
export const rankSolt = (data = {}) => {
  return http.post("/avt/api/rank/slot", data, { type: "formData" });
};
```

**差异分析：** 仅函数名不同 (getRankjili vs rankSolt)，其他完全相同

## 潜在重复接口

### 1. 充值记录接口

**位置：**

- `src/api/user.ts` (第 215-217 行) - 函数名： rechargeRecord

**说明：** 此接口在 user.ts 中定义，但功能上更适合放在 deposit.ts 中

### 2. 获取活动配置接口

**位置：**

- `src/api/user.ts` (第 220-222 行) - 函数名：getConfigEnum
- `src/api/activity.ts` (第 24-26 行) - 函数名：getAdjustmentList

**接口详情：**

```typescript
// user.ts 版本
export const getConfigEnum = (params = {}) => {
  return http.get("/avt/api/activity/adjustment/enum", { params });
};

// activity.ts 版本
export const getAdjustmentList = (params = {}) => {
  return http.get("/avt/api/activity/adjustment/enum", { params });
};
```

**差异分析：** 完全相同的接口，仅函数名不同

## 建议优化方案

### 1. 短期优化

1. **统一接口位置**：将相同功能的接口合并到最合适的文件中
2. **统一函数命名**：采用一致的命名规范
3. **统一配置参数**：确保相同接口的配置参数一致

### 2. 长期重构

1. **模块化重组**：按业务功能重新组织 API 文件结构
2. **创建共享接口**：将通用接口提取到公共模块
3. **类型定义统一**：为所有接口添加完整的 TypeScript 类型定义

### 3. 具体建议

1. **手机号相关接口**：建议保留在 `setPhoneNumber.ts` 中，从 `user.ts` 中移除
2. **活动相关接口**：建议统一在 `activity.ts` 中，从其他文件移除重复定义
3. **游戏相关接口**：建议保留在 `games.ts` 中
4. **充值记录接口**：建议移动到 `deposit.ts` 中

## 注意事项

1. 在删除重复接口前，需要检查所有引用位置
2. 确保合并后的接口配置能满足所有使用场景
3. 建议先进行测试，确保功能正常后再进行清理

---

_生成时间：2025-08-20_
_分析范围：src/api 目录下所有 .ts 文件_
