# Xpage 组件

## 概述

`Xpage` 是一个全页面容器组件，提供导航栏和滚动处理功能。它具有动态导航栏显示、自定义返回处理、加载状态管理等功能，专为移动端页面设计。

## 文件位置

```
src/components/Single-purpose/Xpage.vue
```

## 功能特性

- **动态导航栏**: 基于滚动位置显示/隐藏导航栏
- **自定义返回处理**: 可自定义返回按钮行为
- **加载状态**: 内置加载状态显示
- **插槽支持**: 灵活的内容和导航栏自定义
- **响应式设计**: 针对移动端优化
- **滚动检测**: 智能滚动位置检测

## 使用方法

### 基础实现

```vue
<template>
  <Xpage navTitle="页面标题">
    <div class="page-content">
      <!-- 页面内容 -->
      <h1>欢迎来到我的页面</h1>
      <p>这里是页面内容...</p>
    </div>
  </Xpage>
</template>

<script setup>
import Xpage from '@/components/Single-purpose/Xpage.vue'
</script>
```

### 带自定义导航栏

```vue
<template>
  <Xpage>
    <template #left-icon="{ showNav, showNavBack }">
      <ZIcon 
        type="icon-close" 
        :color="showNav ? '#000' : '#fff'"
        v-show="showNavBack"
        @click="handleClose"
      />
    </template>
    
    <template #title>
      <span class="custom-title">自定义标题</span>
    </template>
    
    <template #right>
      <button @click="handleAction">操作</button>
    </template>
    
    <div class="content">
      <!-- 页面内容 -->
    </div>
  </Xpage>
</template>

<script setup>
import Xpage from '@/components/Single-purpose/Xpage.vue'

const handleClose = () => {
  // 自定义关闭逻辑
}

const handleAction = () => {
  // 自定义操作逻辑
}
</script>
```

### 带加载状态

```vue
<template>
  <Xpage 
    navTitle="数据页面"
    :loading="isLoading"
  >
    <div v-if="!isLoading" class="data-content">
      <!-- 数据内容 -->
      <div v-for="item in dataList" :key="item.id">
        {{ item.name }}
      </div>
    </div>
  </Xpage>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Xpage from '@/components/Single-purpose/Xpage.vue'

const isLoading = ref(true)
const dataList = ref([])

onMounted(async () => {
  try {
    // 模拟数据加载
    const response = await api.getData()
    dataList.value = response.data
  } finally {
    isLoading.value = false
  }
})
</script>
```

### 自定义返回处理

```vue
<template>
  <Xpage 
    navTitle="确认页面"
    @back="handleBack"
  >
    <div class="confirmation-content">
      <p>您确定要离开此页面吗？</p>
      <button @click="confirmLeave">确认离开</button>
      <button @click="cancelLeave">取消</button>
    </div>
  </Xpage>
</template>

<script setup>
import { useRouter } from 'vue-router'
import Xpage from '@/components/Single-purpose/Xpage.vue'

const router = useRouter()

const handleBack = (event) => {
  // 阻止默认返回行为
  event.preventDefault()
  
  // 显示确认对话框
  if (confirm('您确定要离开此页面吗？')) {
    router.back()
  }
}

const confirmLeave = () => {
  router.back()
}

const cancelLeave = () => {
  // 取消操作
}
</script>
```

## 属性 (Props)

| 属性 | 类型 | 必需 | 默认值 | 描述 |
|------|------|------|--------|------|
| `navTitle` | `String` | 否 | `""` | 导航栏标题 |
| `showNavBack` | `Boolean` | 否 | `true` | 是否显示返回按钮 |
| `navBarStyle` | `Object` | 否 | `{}` | 导航栏样式配置 |
| `navShowThreshold` | `Number` | 否 | `50` | 导航栏显示的滚动阈值（px） |
| `backgroundColor` | `String` | 否 | `"#fff"` | 页面背景色 |
| `initShowNav` | `Boolean` | 否 | `false` | 初始是否显示导航栏 |
| `loading` | `Boolean` | 否 | `false` | 是否显示加载状态 |

### navBarStyle 配置

```javascript
{
  backgroundColor: "#fff", // 导航栏背景色
  color: "#000",          // 导航栏文字颜色
  fontSize: "20px"        // 标题字体大小
}
```

## 事件 (Events)

| 事件名 | 参数 | 描述 |
|--------|------|------|
| `back` | `CustomEvent` | 返回按钮点击事件，可通过 `event.preventDefault()` 阻止默认行为 |

## 插槽 (Slots)

| 插槽名 | 作用域参数 | 描述 |
|--------|------------|------|
| `default` | - | 页面主要内容 |
| `left-icon` | `{ showNav, showNavBack }` | 自定义左侧图标 |
| `title` | - | 自定义标题内容 |
| `right` | - | 自定义右侧内容 |

## 行为特性

### 导航栏显示逻辑
1. **初始状态**: 根据 `initShowNav` 决定初始显示状态
2. **滚动检测**: 当滚动超过 `navShowThreshold` 时显示导航栏
3. **动画过渡**: 使用 CSS 过渡实现平滑显示/隐藏

### 返回按钮处理
1. **默认行为**: 调用 `router.back()` 返回上一页
2. **自定义处理**: 通过监听 `back` 事件并调用 `event.preventDefault()` 阻止默认行为
3. **事件传播**: 支持自定义返回逻辑

### 加载状态管理
- **加载中**: 显示 `ZLoading` 组件
- **加载完成**: 显示页面内容
- **iOS 兼容**: 针对 iOS Safari 优化

## 技术实现

### 滚动检测

```javascript
const handleScroll = () => {
  if (!container.value) return;
  showNav.value = container.value.scrollTop > props.navShowThreshold;
};
```

### 返回处理

```javascript
const onBack = (event?: Event) => {
  // 创建一个自定义事件对象来检查是否被阻止
  const backEvent = new CustomEvent("back", { cancelable: true });
  emit("back", backEvent);

  // 如果父组件没有阻止默认行为，则自动返回
  if (!backEvent.defaultPrevented) {
    router.back();
  }
};
```

## 样式设计

### 页面容器
```css
.xpage-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow-y: auto;
  background: #fff;
}
```

### 导航栏
```css
.xpage-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 48px;
  display: flex;
  align-items: center;
  z-index: 10;
  transition: all 0.2s;
  padding: 0 16px;
  background: transparent;
}
```

### 加载容器
```css
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  position: relative;
  z-index: 10;
}
```

## 使用场景

### 详情页面
```vue
<template>
  <Xpage navTitle="商品详情">
    <div class="product-detail">
      <img :src="product.image" alt="商品图片" />
      <h1>{{ product.name }}</h1>
      <p>{{ product.description }}</p>
      <div class="price">￥{{ product.price }}</div>
    </div>
  </Xpage>
</template>
```

### 表单页面
```vue
<template>
  <Xpage navTitle="编辑资料" @back="handleFormBack">
    <form @submit="handleSubmit">
      <div class="form-group">
        <label>姓名</label>
        <input v-model="form.name" type="text" />
      </div>
      <div class="form-group">
        <label>邮箱</label>
        <input v-model="form.email" type="email" />
      </div>
      <button type="submit">保存</button>
    </form>
  </Xpage>
</template>

<script setup>
const handleFormBack = (event) => {
  if (hasUnsavedChanges.value) {
    event.preventDefault()
    // 显示保存提示
  }
}
</script>
```

### 列表页面
```vue
<template>
  <Xpage navTitle="消息列表">
    <div class="message-list">
      <div 
        v-for="message in messages" 
        :key="message.id"
        class="message-item"
      >
        <h3>{{ message.title }}</h3>
        <p>{{ message.content }}</p>
        <span class="time">{{ message.time }}</span>
      </div>
    </div>
  </Xpage>
</template>
```

## 浏览器兼容性

- **现代浏览器**: 完全支持所有功能
- **移动端 Safari**: 针对 iOS 优化
- **Android Chrome**: 完全兼容
- **触摸设备**: 优化的触摸交互

## 故障排除

### 常见问题

1. **导航栏不显示**
   - 检查 `navShowThreshold` 设置
   - 验证页面内容高度是否足够滚动
   - 确认 `initShowNav` 设置

2. **返回按钮无效**
   - 检查是否正确监听 `back` 事件
   - 验证路由配置
   - 确认事件处理逻辑

3. **加载状态异常**
   - 检查 `loading` 属性绑定
   - 验证加载逻辑的异步处理
   - 确认错误处理机制

4. **样式问题**
   - 检查 `navBarStyle` 配置
   - 验证 CSS 优先级
   - 确认移动端适配

## 使用建议

### 最佳实践
- 为重要操作页面添加返回确认
- 合理设置导航栏显示阈值
- 在数据加载时使用加载状态
- 测试不同设备上的滚动体验

### 注意事项
- 组件占用全屏高度，确保正确的页面布局
- 导航栏为固定定位，注意 z-index 层级
- 在 iOS 设备上测试滚动性能
- 考虑页面内容的最小高度要求
