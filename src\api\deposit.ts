import http from "@/utils/http";

// 获取充值项，判断是否首次充值等
export const getDepositRule = () => {
  return http.post(
    "/common/api/global-config/first/recharge/rule",
    {},
    {
      type: "formData",
    }
  );
};
export const rechargeWithdraw = (data = {}) => {
  return http.post("/common/api/global-config/recharge-withdraw", data);
};

// 充值
export const paymentBalanceAdd = (data: object) => {
  return http.post("/common/api/payment/balance-add", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// webPay gcash充值
export const paymentGcash = (data: object) => {
  return http.post("/open/api/gcash/payment", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};
// webPay gcash充值
export const paymentMaya = (data: object) => {
  return http.post("/open/api/maya/payment", data, {
    type: "formData",
    transformResult: (res) => res.data,
  });
};

//gcash内购 跳回小程序
export const gcashSetJumpType = (data = {}) => {
  return http.post("/common/api/gcash/set/jumpType", data, {
    transformResult: (res) => res.data,
  });
};
