<template>
  <ZPopOverlay :show="showRegisterBonusTip">
    <div class="content" :style="{ backgroundImage: `url(${activityBonus5Image})` }">
      <div class="content-wrap">
        <div class="tips">{{ registerTitle }}</div>
        <div class="bonus-wrap">
          <IconCoin class="icon" :size="40" />
          <span class="bonus">{{ formattedBonusAmount }}</span>
        </div>

        <div class="btn" ref="confirmBtnRef">
          <GradientButton
            text="Done"
            background-gradient=" linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"
            border-gradient="#FFDFBF"
            :showLight="true"
            @click="confirmClick"
          />
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { AWARD_UPDATE_TYPE, AWARD_NAME } from "@/utils/config/GlobalConstant";
import { useGlobalStore } from "@/stores/global";
// 导入图片资源，确保与预加载使用相同的路径
import activityBonus5Image from "@/assets/images/popDialog/activityBonus5.png";

const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { showRegisterBonusTip, activityBonusType } = storeToRefs(autoPopMgrStore);
const { registerAward } = storeToRefs(globalStore);

// 注册奖励相关变量
const registerTitle = ref("");
const registerCoin = ref("");

const emit = defineEmits(["start-coin-animation"]);
const confirmBtnRef = ref<HTMLElement | null>(null);

// 格式化金额，如果 registerCoin 为数字则以本地格式显示，否则显示 "--"
const formattedBonusAmount = computed(() => {
  const num = Number(registerCoin.value?.replace("-", ""));
  return num >= 0 ? num.toLocaleString() : "--";
});

// 关闭弹窗，清理后调用余额刷新和销毁当前弹窗
const confirmClick = async () => {
  showRegisterBonusTip.value = false;
  emit("start-coin-animation", confirmBtnRef.value);
  // 防止重复弹框
  globalStore.updateRegisterAward({ type: 0 });
  // Balance 组件内部会更新余额
  setTimeout(() => {
    AutoPopMgr.destroyCurrentPopup();
  }, 2000);
};

// 总初始化函数，根据奖励数据类型进行区分处理
const initBonusData = async () => {
  let updateType = activityBonusType.value;
  // 如果是注册奖励，根据不同 updateType 填写对应标题及金额
  const amount = registerAward.value.amount;
  registerTitle.value =
    updateType === AWARD_UPDATE_TYPE.REGISTER_USER
      ? AWARD_NAME.REGISTER_USER
      : AWARD_NAME.BING_IPHONE_USER;
  registerCoin.value = amount + "";
};

// 监听弹窗显示
watch(
  [() => autoPopMgrStore.showRegisterBonusTip, () => autoPopMgrStore.activityBonusType],
  ([show]) => {
    if (show) {
      initBonusData();
    }
  }
);

defineExpose({
  confirmBtnRef,
});
</script>

<style lang="scss" scoped>
.content {
  width: 375px;
  height: 667px;
  padding: 20px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  .content-wrap {
    position: absolute;
    top: 315px;
    left: 50%;
    transform: translateX(-50%);
  }
  .tips {
    width: 280px;
    color: #222;
    font-family: Inter;
    font-size: 16px;
    font-weight: 500;
    line-height: 28px;
    text-align: center;
  }

  .bonus-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0 20px;
    .icon {
      margin-right: 10px;
    }

    .bonus {
      color: #222;
      font-family: "D-DIN";
      font-size: 36px;
      font-weight: 700;
      line-height: 28px;
    }
  }

  .btn {
    width: 220px;
    height: 56px;
    overflow: hidden;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    margin: 0 auto;
  }

  .close {
    position: absolute;
    top: 550px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
