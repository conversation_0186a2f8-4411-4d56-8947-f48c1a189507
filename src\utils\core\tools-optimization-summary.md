# tools.ts 优化总结

## 优化内容

### 1. 代码结构重组

- 按功能模块重新组织代码，使用清晰的分隔符和注释
- 将相关功能的函数归类到同一模块下
- 删除了所有重复的函数定义

### 2. 类型安全改进

- 为所有函数添加了完整的 TypeScript 类型注解
- 改进了函数参数和返回值的类型定义
- 使用更严格的类型检查，避免 `any` 类型的滥用

### 3. 函数优化

- 统一了函数命名规范，使用 function 声明替代部分箭头函数
- 优化了函数实现逻辑，提高代码可读性
- 改进了错误处理和边界条件检查

### 4. 注释规范化

- 使用标准的 JSDoc 格式为所有函数添加详细注释
- 明确了每个函数的用途、参数说明和返回值
- 添加了模块级别的分类注释

## 功能模块分类

### URL 和查询参数处理工具

- `getQueryByUrl()` - 解析 URL 查询参数
- `getQuery()` - 将参数对象转换为查询字符串

### 数据类型检查和验证工具

- `isNull()` - 判断是否为 null 或 undefined
- `getTag()` - 获取值的精确数据类型
- `isEmpty()` - 判断值是否为空
- `hasValue()` - 判断值是否有效
- `isPureDigits()` - 判断是否为纯数字字符串
- `isNumeric()` - 判断是否为数字
- `hasNumberAndCharacter()` - 判断是否同时包含数字和字母

### 时间和日期处理工具

- `parseTime()` - 格式化时间为指定格式
- `formatTime()` - 格式化时间戳为时间字符串
- `formatDateToEventString()` - 格式化日期为事件字符串
- `formatEngDate()` - 格式化英文日期时间
- `getToday()` - 获取今天的日期字符串

### 函数工具

- `throttleFirst()` - 节流函数

### 日期区间计算工具

- `getWeekPeriods()` - 获取本周与上周的期间值
- `getCurrentHalfMonthPeriod()` - 获取当前半月区间
- `getNextHalfMonthStart()` - 计算下一个半月区间的起始时间
- `getYearsoldWithDate()` - 根据出生日期计算年龄

### 数字和字符串处理工具

- `stringToInt()` - 字符串转整数
- `formatNumberToThousands()` - 数字转千分位格式（合并了原有的两个重复函数）
- `amountStrToNumber()` - 千分位字符串转数字
- `amountFormatThousands()` - 数字转千分位字符串（已标记为废弃，建议使用 formatNumberToThousands）
- `maskString()` - 隐藏字符串中间部分

### 随机数和数组工具

- `getRandomInt()` - 获取随机整数
- `generateRandomId()` - 生成随机唯一 ID
- `areArraysEqualIgnoreOrder()` - 数组一致性对比
- `sortArray()` - 数组倒序排序

### 平台检测和验证工具

- `getOS()` - 检测操作系统类型
- `getBrowserName()` - 获取浏览器名称
- `myOS()` - 获取自定义操作系统编号
- `isPhilippinePhoneNumber()` - 验证菲律宾手机号码

### 数据处理和格式化工具

- `groupData()` - 按照日期分组数据

### 特殊业务逻辑工具

- `sortFunc()` - 游戏列表排序算法

### 位运算和格式化工具

- `getBitValue()` - 获取数字指定位的值
- `formatPhoneNumber()` - 格式化手机号码显示
- `maskAccountNumber()` - 格式化账号显示

### 图片和资源工具

- `getServerSideImageUrl()` - 获取服务器端图片完整链接
- `getUserAvatar()` - 获取用户头像 URL
- `preloadImages()` - 预加载图片

## 优化效果

1. **代码可维护性提升** - 清晰的模块划分和规范的注释
2. **类型安全性增强** - 完整的 TypeScript 类型支持
3. **代码重复消除** - 删除了所有重复的函数定义
4. **函数合并优化** - 将 `formatNumberToThousands` 和 `amountFormatThousands` 合并为一个更强大的函数
5. **性能优化** - 改进了部分函数的实现逻辑
6. **开发体验改善** - 更好的 IDE 支持和代码提示

## 最新优化：函数合并

### formatNumberToThousands 函数增强

合并了原有的 `formatNumberToThousands` 和 `amountFormatThousands` 两个功能重复的函数，新函数特性：

- **统一接口**：支持数字和字符串输入
- **灵活配置**：通过 options 参数支持多种格式化选项
- **向下兼容**：保留了 `amountFormatThousands` 作为废弃函数，确保现有代码不受影响
- **功能增强**：
  - `precision`: 可配置小数位数
  - `showSmallAmount`: 可选择是否显示小额提示（< 0.01 显示 "<0.01"）
  - `returnOriginalIfEmpty`: 可选择空值时的处理方式

### 使用示例

```typescript
// 基础用法（替代原 formatNumberToThousands）
formatNumberToThousands(1234.56); // "1,234.56"
formatNumberToThousands(1234.56, { precision: 0 }); // "1,235"

// 高级用法（替代原 amountFormatThousands）
formatNumberToThousands(0.005, { showSmallAmount: true }); // "<0.01"
formatNumberToThousands(null, { returnOriginalIfEmpty: true }); // null
```

## 注意事项

- 部分函数使用了已弃用的 Web API（如 `navigator.platform`），建议后续替换为现代 API
- `numeral` 库缺少类型定义，建议安装 `@types/numeral` 或添加类型声明文件
