import numeral from "numeral";

export const isIOS = /iphone|ipad|ipod/i.test(navigator.userAgent);

// ================================
// URL 和查询参数处理工具
// ================================

/**
 * 通过 URL 字符串解析查询参数
 * @param url - URL 字符串
 * @returns 查询参数对象
 */
export function getQueryByUrl(url: string): Record<string, string> {
  const decodedUrl = decodeURIComponent(url);
  const queryString = decodedUrl.replace("?", "");

  if (!queryString) return {};

  return queryString.split("&").reduce((params: Record<string, string>, param: string) => {
    const [key, value = ""] = param.split("=");
    if (key) {
      params[key] = value;
    }
    return params;
  }, {});
}

/**
 * 将参数对象转换为查询字符串
 * @param params - 参数对象
 * @returns 查询字符串
 */
export function getQuery(params: Record<string, unknown>): string {
  return Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");
}

// ================================
// 数据类型检查和验证工具
// ================================

/**
 * 判断值是否为 null 或 undefined（包括字符串形式）
 * @param val - 要判断的值
 * @returns 是否为 null 或 undefined
 */
export function isNull(val: unknown): boolean {
  return !!String(val)
    .trim()
    .match(/^(null|undefined)$/i);
}

/**
 * 获取值的精确数据类型
 * @param val - 要检查的值
 * @returns 类型标签字符串
 */
export function getTag(val: unknown): string {
  if (val == null) {
    return val === undefined ? "[object Undefined]" : "[object Null]";
  }
  return Object.prototype.toString.call(val);
}

/**
 * 判断值是否为空（包括各种空值情况）
 * @param val - 要判断的值
 * @returns 是否为空
 */
export function isEmpty(val: unknown): boolean {
  // 检查 null 和 undefined
  if (isNull(val)) return true;

  // 检查日期对象
  if (getTag(val) === "[object Date]") {
    return isNaN((val as Date).getTime());
  }

  // 检查对象
  if (getTag(val) === "[object Object]" && val) {
    return Object.keys(val as object).length === 0;
  }

  // 检查字符串形式的空值
  const stringVal = String(val).trim();
  return stringVal === "" || stringVal === "{}" || stringVal === "[]";
}

/**
 * 判断值是否有效（0 和 "0" 被认为是有效值）
 * @param value - 要检查的值
 * @returns 是否有效
 */
export function hasValue(value: unknown): boolean {
  if (value === 0 || value === "0") return true;
  return !!value;
}

/**
 * 2024-05-13 00:00:00格式的时间转换为时间戳
 * @param time
 * @returns
 */

export function timeToTimestamp() {
  let tempstr = time + "";
  let timearr = tempstr.slice(0, 19).replace(/\-/g, "/"); //有得浏览器不支持
  let timestamp = Date.parse(timearr);
  //timestamp = timestamp / 1000; //时间戳为13位需除1000，时间戳为10位不需除1000
  //console.log(time + "的时间戳为：" + timestamp);
  return timestamp;
}

/**
 * 判断字符串是否为纯数字
 * @param str - 要检查的字符串
 * @returns 是否为纯数字
 */
export function isPureDigits(str: string): boolean {
  return /^[0-9]+$/.test(str);
}

/**
 * 判断值是否为数字
 * @param value - 要检查的值
 * @returns 是否为数字
 */
export function isNumeric(value: unknown): boolean {
  return !isNaN(parseFloat(value as string)) && isFinite(value as number);
}

/**
 * 判断字符串是否同时包含数字和字母
 * @param input - 输入字符串
 * @returns 是否同时包含数字和字母
 */
export function hasNumberAndCharacter(input: string): boolean {
  const hasNumber = /\d/.test(input);
  const hasCharacter = /[a-zA-Z]/.test(input);
  return hasNumber && hasCharacter;
}

// ================================
// 时间和日期处理工具
// ================================

/**
 * 格式化时间 - 将 Date 转化为指定格式的字符串
 * @param date - 要转换的日期
 * @param fmt - 指定格式，默认为 'yyyy-MM-dd hh:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function parseTime(date: string | number | Date, fmt = "yyyy-MM-dd hh:mm:ss"): string {
  fmt = fmt.replaceAll("Y", "y").replaceAll("D", "d");

  if (isEmpty(date)) return "";

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return "";

  const weeks = ["一", "二", "三", "四", "五", "六", "日"];
  const formatMap: Record<string, number | string> = {
    "M+": dateObj.getMonth() + 1, // 月份
    "d+": dateObj.getDate(), // 日
    "h+": dateObj.getHours(), // 小时
    "m+": dateObj.getMinutes(), // 分
    "s+": dateObj.getSeconds(), // 秒
    "q+": Math.floor((dateObj.getMonth() + 3) / 3), // 季度
    S: dateObj.getMilliseconds(), // 毫秒
    a: weeks[dateObj.getDay() - 1], // 周
  };

  // 处理年份
  const yearMatch = /(y+)/.exec(fmt);
  if (yearMatch) {
    const yearStr = dateObj.getFullYear().toString();
    fmt = fmt.replace(yearMatch[0], yearStr.substring(4 - yearMatch[0].length));
  }

  // 处理其他格式
  for (const [pattern, value] of Object.entries(formatMap)) {
    const regex = new RegExp(`(${pattern})`);
    const match = regex.exec(fmt);
    if (match) {
      const replacement =
        match[0].length === 1 ? String(value) : String(value).padStart(match[0].length, "0");
      fmt = fmt.replace(match[0], replacement);
    }
  }

  return fmt;
}

/**
 * 格式化时间戳为时间字符串
 * @param timestamp - 时间戳（秒）
 * @param includeSeconds - 是否包含秒
 * @returns 格式化的时间字符串
 */
export function formatTime(timestamp: number, includeSeconds = false): string {
  const date = new Date(timestamp * 1000);
  const options: Intl.DateTimeFormatOptions = {
    hour: "2-digit",
    minute: "2-digit",
    second: includeSeconds ? "2-digit" : undefined,
    hour12: false,
  };
  return new Intl.DateTimeFormat("zh-CN", options).format(date);
}

/**
 * 格式化日期为事件字符串格式
 * @param dateInput - 日期输入
 * @returns 格式化的日期字符串 "MMM DD, YYYY"
 */
export function formatDateToEventString(dateInput: string | number): string {
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) {
    throw new Error("Invalid date input provided.");
  }
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  return new Intl.DateTimeFormat("en-US", options).format(date);
}

/**
 * 格式化英文日期时间
 * @param dateStr - 日期字符串
 * @returns 格式化的英文日期时间 "Jun 25,2025 18:04:02"
 */
export function formatEngDate(dateStr: string): string {
  const date = new Date(dateStr.replace(" ", "T"));
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const month = months[date.getMonth()];
  const day = date.getDate();
  const year = date.getFullYear();
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return `${month} ${day},${year} ${hours}:${minutes}:${seconds}`;
}

/**
 * 获取今天的日期字符串
 * @returns 今天的日期 "YYYY-M-D"
 */
export function getToday(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const date = now.getDate();
  return `${year}-${month}-${date}`;
}

// ================================
// 函数工具
// ================================

/**
 * 节流函数 - 限制函数在指定时间内只能执行一次
 * @param func - 要节流的函数
 * @param limit - 时间限制（毫秒）
 * @returns 节流后的函数
 */
export function throttleFirst<T extends (...args: any[]) => void>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) & { reset: () => void } {
  let lastExec = 0;
  let timeout: ReturnType<typeof setTimeout>;

  const reset = () => {
    lastExec = 0;
    clearTimeout(timeout);
  };

  const throttled = function (this: unknown, ...args: Parameters<T>) {
    const now = Date.now();
    if (now - lastExec >= limit) {
      func.apply(this, args);
      lastExec = now;
      timeout = setTimeout(reset, limit);
    }
  } as any;

  throttled.reset = reset;
  return throttled;
}

// ================================
// 日期区间计算工具
// ================================

/**
 * 获取本周与上周的期间值字符串
 * @returns 包含上周和本周日期区间的对象
 */
export function getWeekPeriods(): { lastWeek: string; thisWeek: string } {
  const now = new Date();
  const day = now.getDay() === 0 ? 7 : now.getDay();
  const monday = new Date(now);
  monday.setDate(now.getDate() - day + 1);

  // 本周区间
  const thisMonday = new Date(monday);
  const thisSunday = new Date(monday);
  thisSunday.setDate(thisMonday.getDate() + 6);

  // 上周区间
  const lastMonday = new Date(monday);
  lastMonday.setDate(thisMonday.getDate() - 7);
  const lastSunday = new Date(monday);
  lastSunday.setDate(thisMonday.getDate() - 1);

  const formatDate = (date: Date) =>
    `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;

  return {
    lastWeek: `${formatDate(lastMonday)} - ${formatDate(lastSunday)}`,
    thisWeek: `${formatDate(thisMonday)} - ${formatDate(thisSunday)}`,
  };
}

/**
 * 计算当前日期属于上半月还是下半月，并返回对应的区间字符串
 * @returns 半月区间数组 [开始日期, 结束日期]
 */
export function getCurrentHalfMonthPeriod(): [string, string] {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  const daysInMonth = new Date(year, month, 0).getDate();

  if (day <= 15) {
    return [`${year}/${month}/1`, `${year}/${month}/15`];
  } else {
    return [`${year}/${month}/16`, `${year}/${month}/${daysInMonth}`];
  }
}

/**
 * 计算下一个半月区间的起始时间
 * @returns 下一个半月区间的开始日期
 */
export function getNextHalfMonthStart(): string {
  const now = new Date();
  let year = now.getFullYear();
  let month = now.getMonth() + 1;
  const day = now.getDate();

  if (day <= 15) {
    return `${year}/${month}/16`;
  } else {
    if (month === 12) {
      year += 1;
      month = 1;
    } else {
      month += 1;
    }
    return `${year}/${month}/1`;
  }
}

/**
 * 根据出生日期计算年龄
 * @param year - 出生年份
 * @param month - 出生月份
 * @param day - 出生日期
 * @returns 年龄
 */
export function getYearsoldWithDate(year: number, month: number, day: number): number {
  const now = new Date();
  const nowYear = now.getFullYear();
  const nowMonth = now.getMonth() + 1;
  const nowDay = now.getDate();

  let age = nowYear - year;

  if (nowMonth < month || (nowMonth === month && nowDay < day)) {
    age -= 1;
  }

  return age;
}

// ================================
// 数字和字符串处理工具
// ================================

/**
 * 字符串转整数（移除千分位分隔符）
 * @param str - 输入字符串
 * @returns 转换后的整数
 */
export function stringToInt(str: string): number {
  const cleanStr = str.replace(/[.,]/g, "");
  const num = parseInt(cleanStr, 10);
  return isNaN(num) ? 0 : num;
}

/**
 * 数字转千分位格式（合并了 formatNumberToThousands 和 amountFormatThousands）
 * @param value - 要格式化的数字或字符串
 * @param options - 格式化选项
 * @param options.precision - 小数位数，默认为 2
 * @param options.showSmallAmount - 是否显示小额提示（< 0.01 显示 "<0.01"），默认为 false
 * @param options.returnOriginalIfEmpty - 当值为空时是否返回原值，默认为 false
 * @returns 格式化后的字符串
 */
export function formatNumberToThousands(
  value: number | string,
  options: {
    precision?: number;
    showSmallAmount?: boolean;
    returnOriginalIfEmpty?: boolean;
  } = {}
): string | number {
  const { precision = 2, showSmallAmount = false, returnOriginalIfEmpty = false } = options;

  // 处理空值情况
  if (!hasValue(value)) {
    return returnOriginalIfEmpty ? value : precision > 0 ? "0.00" : "0";
  }

  // 转换为数字
  let numValue: number;
  if (typeof value === "string") {
    numValue = parseFloat(value);
  } else if (typeof value === "number") {
    numValue = value;
  } else {
    return precision > 0 ? "0.00" : "0";
  }

  // 验证数字有效性
  if (!isFinite(numValue) || isNaN(numValue)) {
    return precision > 0 ? "0.00" : "0";
  }

  // 处理小额显示
  if (showSmallAmount && numValue > 0 && numValue < 0.01) {
    return "<0.01";
  }

  // 格式化数字
  return numValue.toLocaleString("en-US", {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  });
}

/**
 * 千分位字符串转数字
 * @param value - 千分位格式的字符串或数字
 * @returns 转换后的数字
 */
export function amountStrToNumber(value: string | number): number | null {
  return numeral(value).value();
}

/**
 * @deprecated 请使用 formatNumberToThousands 替代
 * 数字转千分位字符串（兼容性保留，建议使用 formatNumberToThousands）
 */
export function amountFormatThousands(value: number, precision = 2): string | number {
  return formatNumberToThousands(value, {
    precision,
    showSmallAmount: true,
    returnOriginalIfEmpty: true,
  });
}

/**
 * 隐藏字符串中间部分
 * @param str - 原字符串
 * @param visibleStart - 开始显示的字符数
 * @param visibleEnd - 结尾显示的字符数
 * @param hideLen - 隐藏部分显示的星号数量
 * @returns 处理后的字符串
 */
export function maskString(str = "", visibleStart = 4, visibleEnd = 3, hideLen = 4): string {
  if (str.length <= visibleStart + visibleEnd) return str;

  const newStr = str.toString();
  const start = newStr.slice(0, visibleStart);
  const end = newStr.slice(-visibleEnd);
  const stars = "*".repeat(hideLen);
  return start + stars + end;
}
// ================================
// 随机数和数组工具
// ================================

/**
 * 获取指定范围内的随机整数
 * @param min - 最小值
 * @param max - 最大值
 * @returns 随机整数
 */
export function getRandomInt(min = 15, max = 25): number {
  const minCeil = Math.ceil(min);
  const maxFloor = Math.floor(max);
  return Math.floor(Math.random() * (maxFloor - minCeil + 1)) + minCeil;
}

/**
 * 生成随机唯一ID
 * @param length - ID长度
 * @returns 随机ID字符串
 */
export function generateRandomId(length = 16): string {
  const bytes = new Uint8Array(length);
  crypto.getRandomValues(bytes);
  return Array.from(bytes, (byte) => byte.toString(16).padStart(2, "0")).join("");
}

/**
 * 生成带前缀的唯一ID
 * @param prefix - 前缀
 * @returns 带前缀的唯一ID
 */
export function generateId(prefix = "id"): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 不考虑顺序的数组一致性对比
 * @param arr1 - 第一个数组
 * @param arr2 - 第二个数组
 * @returns 是否相等
 */
export function areArraysEqualIgnoreOrder(arr1: unknown[], arr2: unknown[]): boolean {
  if (!Array.isArray(arr1) || !Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length) return false;
  return JSON.stringify([...arr1].sort()) === JSON.stringify([...arr2].sort());
}

/**
 * 数组倒序排序
 * @param array - 要排序的数组
 * @param key - 排序字段
 * @returns 排序后的数组
 */
export function sortArray<T>(array: T[], key: keyof T): T[] {
  return [...array].sort((a, b) => (b[key] as number) - (a[key] as number));
}

// ================================
// 平台检测和验证工具
// ================================

/**
 * 检测操作系统类型
 * @returns 操作系统类型
 */
export function getOS(): string {
  const userAgent = navigator.userAgent || navigator.platform || "";
  if (/iphone|ipad|ipod/i.test(userAgent)) return "ios";
  if (/android/i.test(userAgent)) return "android";
  if (/win/i.test(userAgent)) return "windows";
  if (/mac/i.test(userAgent)) return "mac";
  if (/linux/i.test(userAgent)) return "linux";
  return "unknown";
}

/**
 * 检测是否在浏览器环境中
 */
export const isBrowser = typeof window !== "undefined" && typeof document !== "undefined";

/**
 * 获取浏览器名称
 * @returns 浏览器名称
 */
export function getBrowserName(): string {
  const userAgent = (
    navigator.userAgent ||
    navigator.vendor ||
    (window as any)?.opera ||
    ""
  ).toLowerCase();

  if (userAgent.includes("windows")) return "windows";
  if (userAgent.includes("macintosh") && userAgent.includes("intel")) return "macOS";
  if (userAgent.includes("iphone")) return "ios";
  if (userAgent.includes("android")) return "android";
  if (userAgent.includes("ipad")) return "ipad";
  return "other";
}

/**
 * 获取自定义操作系统编号
 * @returns 操作系统编号
 */
export function myOS(): number | null {
  const os = getBrowserName();
  if (os === "windows" || os === "macOS") return 4;
  if (os === "ios" || os === "ipad") return isBrowser ? 1 : 3;
  if (os === "android") return isBrowser ? 0 : 2;
  return null;
}

/**
 * 验证菲律宾手机号码
 * @param phoneNumber - 手机号码
 * @returns 是否为有效的菲律宾手机号码
 */
export function isPhilippinePhoneNumber(phoneNumber: string): boolean {
  const regex = /^(09|9|\+639|8|08)\d{9}$/;
  return regex.test(phoneNumber);
}
// ================================
// 数据处理和格式化工具
// ================================

/**
 * 按照日期分组数据
 * @param list - 数据列表
 * @param field - 日期字段名
 * @returns 按日期分组的数据
 */
export function groupData<T>(list: T[], field = "third_updated_time"): Record<string, T[]> {
  return list.reduce((acc: Record<string, T[]>, item: any) => {
    const dateArr = new Date(item[field]).toDateString().split(" ");
    dateArr.shift();
    const lastDate = dateArr.pop();
    const date = dateArr.join(" ") + "," + lastDate;

    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(item);
    return acc;
  }, {});
}

/**
 * 获取数字指定位的值
 * @param num - 32位整数
 * @param position - 位置（0-31）
 * @returns 指定位的值
 */
export function getBitValue(num: number, position: number): number {
  if (
    Number.isInteger(num) &&
    num >= -2147483648 &&
    num <= 2147483647 &&
    Number.isInteger(position) &&
    position >= 0 &&
    position <= 31
  ) {
    return (num >> (position - 1)) & 1;
  } else {
    console.error("参数不合法");
    return -1;
  }
}

/**
 * 格式化手机号码显示
 * @param phoneNumber - 手机号码
 * @returns 格式化后的手机号码
 */
export function formatPhoneNumber(phoneNumber: string): string {
  if (phoneNumber.length !== 10) return "";
  return `${phoneNumber.slice(0, 2)}****${phoneNumber.slice(6, 10)}`;
}

/**
 * 格式化账号显示
 * @param accountNo - 账号
 * @returns 格式化后的账号
 */
export function maskAccountNumber(accountNo: string): string {
  if (!accountNo) return accountNo;

  let formattedAccount = accountNo;
  if (accountNo.length === 10) {
    formattedAccount = "0" + accountNo;
  }

  if (formattedAccount.length < 11) return accountNo;
  return formattedAccount.slice(0, 4) + "****" + formattedAccount.slice(8, 11);
}

// ================================
// 图片和资源工具
// ================================

const AssetsUrl = import.meta.env.VITE_ASSETS_URL;

/**
 * 获取服务器端图片完整链接
 * @param path - 图片路径
 * @returns 完整的图片URL
 */
export function getServerSideImageUrl(path: string): string {
  if (/^https?:\/\//.test(path)) return path;
  return AssetsUrl + path;
}

/**
 * 获取用户头像URL
 * @param avatar - 头像路径或数字
 * @returns 头像URL
 */
export function getUserAvatar(avatar: string | number): string {
  if (!avatar || isPureDigits(String(avatar))) {
    avatar = "images/38441c19a785edac53ace52c45f662e7.png";
  }
  return AssetsUrl + avatar;
}

/**
 * 预加载图片
 * @param imageUrls - 图片URL数组
 */
export function preloadImages(imageUrls: string[]): void {
  imageUrls.forEach((url) => {
    const img = new Image();
    img.src = url;
  });
}

export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 格式化卡号：按照 4-4-3 的格式显示，中间部分用 **** 替换
export const formatCardNumber = (cardNumber: string) => {
  if (!cardNumber) return "";

  if (cardNumber.length < 8) {
    return cardNumber; // 如果号码太短，直接返回原始值
  }

  // 取前4位
  const first4 = cardNumber.slice(0, 4);
  // 取后3位
  const last3 = cardNumber.slice(-3);

  // 返回格式化后的字符串：前4位 + 空格 + **** + 空格 + 后3位
  return `${first4}  ****  ${last3}`;
};
