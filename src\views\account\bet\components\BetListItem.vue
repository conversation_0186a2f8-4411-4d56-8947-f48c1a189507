<template>
  <van-cell class="items-block">
    <div class="items-title">{{ dateKey }}</div>
    <div class="items-content">
      <div v-for="item in items" :key="item.id">
        <LineSetting
          valueStatus="normal"
          :text="item.game_name"
          :value="item.provider_name"
          :showArrow="false"
          :rightText="getRightText(item)"
          :rightStyle="getRightStyle()"
          @click="handleItemClick(item)"
        >
          <template #icon>
            <img class="item-img" v-lazy="getGameImage(item)" alt="" />
          </template>
        </LineSetting>
      </div>
    </div>
  </van-cell>
</template>

<script setup lang="ts">
import LineSetting from "@/views/account/components/LineSetting.vue";
import PlaceholderBase64 from "@/assets/constants/nsLogoBase64";
import { formatNumberToThousands } from "@/utils/core/tools";
import type { BetListItemProps, BetItem } from "../shared";
import { TAB_STYLES } from "../shared";

interface Emits {
  (e: "item-click", item: BetItem): void;
}

const props = defineProps<BetListItemProps>();
const emit = defineEmits<Emits>();

// Computed properties
const getRightText = (item: BetItem) => {
  let text = formatNumberToThousands(item.win_lose / 100);
  if (props.tabType === "Unsettled") {
    text = "Bet " + formatNumberToThousands(item.bet / 100);
  }
  if (props.tabType === "Promos") {
    text = "+" + text;
  }
  return text;
};

const getRightStyle = () => {
  return TAB_STYLES[props.tabType as keyof typeof TAB_STYLES] || TAB_STYLES.Settled;
};

const getGameImage = (item: BetItem) => {
  return props.gamesList[item.game_id]?.images || PlaceholderBase64;
};

// Event handlers
const handleItemClick = (item: BetItem) => {
  const enrichedItem = {
    ...item,
    images: getGameImage(item),
  };
  emit("item-click", enrichedItem);
};
</script>

<style lang="scss" scoped>
.items-block {
  background: transparent;
}

.item-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.items-content {
  padding: 16px 12px 0;
  background-color: #fff;
  text-align: left;
  border-radius: 10px;

  &:deep(.line-item) {
    padding: 0 0 14px 0;
  }
}

.items-title {
  color: #6a7a88;
  font-family: Inter;
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  border-bottom: 0.5px solid #eee;
  line-height: 48px;
  padding: 0 12px;
  text-align: left;
}
</style>
