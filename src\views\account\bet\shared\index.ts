// ===== TYPES =====

// Shared types for bet module
export interface BetItem {
  id: string | number;
  game_id: string | number;
  game_name: string;
  provider_name: string;
  win_lose: number;
  bet: number;
  effective_bet?: number;
  payouts?: number;
  house_rake?: number;
  total_refund?: number;
  third_create_time: string;
  third_updated_time?: string;
  index_time?: string;
  status?: string | number;
  game_type?: string;
  images?: string;
  [key: string]: any;
}

export interface BetOrder {
  count: number;
  total_page: number;
  current_page: number;
  list: Record<string, BetItem[]>;
}

export interface FilterProvider {
  id: string | number;
  provider: string;
}

export interface DateOption {
  name: string;
  value: number;
}

export interface GameInfo {
  id: string | number;
  images?: string;
  [key: string]: any;
}

// API request parameters
export interface BetOrderParams {
  provider?: string;
  page: number;
  page_number: number;
  status: string | number;
  date: number;
  [key: string]: any;
}

// Component props interfaces
export interface BetFiltersProps {
  activeBtn: DateOption;
  filterProvider: FilterProvider[];
  dateOptions: DateOption[];
}

export interface BetTabsProps {
  betOrders: Record<string, BetOrder>;
  activeTab: string;
  loading: boolean;
  gamesList: Record<string | number, GameInfo>;
  tabTitles: Record<string, string>;
}

export interface BetListItemProps {
  dateKey: string;
  items: BetItem[];
  tabType: string;
  gamesList: Record<string | number, GameInfo>;
}

// ===== CONSTANTS =====

// Tab configuration
export const TAB_TITLES = {
  3: "Settled",
  1: "Unsettled", 
  2: "Cancel",
  promos: "Promos",
  Settled: "3",
  Unsettled: "1",
  Cancel: "2",
  Promos: "promos",
} as const;

// Tab status mapping
export const TAB_STATUS_MAP = {
  Settled: 3,
  Unsettled: 1,
  Cancel: 2,
  Promos: "promos",
} as const;

// Date filter options
export const DATE_OPTIONS: DateOption[] = [
  { name: "Today", value: 1 },
  { name: "Yesterday", value: 2 },
  { name: "Last 3 days", value: 3 },
  { name: "Last 7 days", value: 7 },
];

// Style configuration for different tab types
export const TAB_STYLES = {
  Settled: { color: "#FF4849", "font-weight": 600 },
  Unsettled: { color: "#909090", "font-weight": 600 },
  Cancel: { color: "#303030", "font-weight": 600 },
  Promos: { color: "#12BE6B", "font-weight": 600 },
} as const;

// Default bet orders structure
export const DEFAULT_BET_ORDERS = {
  Settled: { count: 0, list: {}, total_page: 0, current_page: 0 },
  Unsettled: { count: 0, list: {}, total_page: 0, current_page: 0 },
  Cancel: { count: 0, list: {}, total_page: 0, current_page: 0 },
  Promos: { count: 0, list: {}, total_page: 0, current_page: 0 },
};

// Default current page structure
export const DEFAULT_CURRENT_PAGE = {
  Settled: 1,
  Unsettled: 1,
  Cancel: 1,
  Promos: 1,
};

// API configuration
export const API_CONFIG = {
  DEFAULT_PAGE_SIZE: 15,
  DEFAULT_PAGE: 1,
} as const;
