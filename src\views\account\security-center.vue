<script setup lang="ts">
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { showToast } from "vant";

import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import LineSetting from "@/views/account/components/LineSetting.vue";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { KycMgr, InGameType, KycState } from "@/utils/KycMgr";
import { formatPhoneNumber } from "@/utils/core/tools";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import { logout, getUserKyc } from "@/api/user";
import { useGlobalStore } from "@/stores/global";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import ZVerifyDialog from "@/components/ZVerifyDialog/index.vue";
import SetChangePhoneDialog from "@/components/ZVerifyDialog/SetChangePhoneDialog.vue";
import SetLoginPasswordDialog from "@/components/ZVerifyDialog/SetLoginPasswordDialog.vue";
import SetPaymentPasswordDialog from "@/components/ZVerifyDialog/SetPaymentPasswordDialog.vue";

// ==================== Store 和路由 ====================
const globalStore = useGlobalStore();
const autoPopMgrStore = useAutoPopMgrStore();
const router = useRouter();
const { userInfo } = storeToRefs(globalStore);

// ==================== 弹窗状态管理 ====================
const dialogStates = ref({
  showUpdatePhone: false,
  showBindPhone: false,
  showInitLoginPassword: false,
  showUpdateLoginPassword: false,
  showInitPaymentPassword: false,
  showUpdatePaymentPassword: false,
  showLogout: false,
});

// ==================== KYC 数据 ====================
const kycDetail = ref({});
const kycData = ref({});
const showKycTip = ref(false);
const kycBonus = ref({
  is_kyc_completed: 1, //是否完成 1完成了，0未完成
  kyc_completed_reward: "15", //kyc 信息完成后的奖励配置
  status: 0, //0不显示 1显示
});

// ==================== 计算属性 ====================
const kycStatusConfig = computed(() => {
  const statusIndex = (kycDetail.value as any)?.status || 0;
  const statusTexts = ["Not Verified", "Completed", "Reviewing", "Rejected"];
  const statusColors = ["#999999", "#01D46A", "#FF936F", "#AC1140"];

  return {
    text: statusTexts[statusIndex] || "",
    color: statusColors[statusIndex] || "#999999",
  };
});

const phoneDisplayValue = computed(() => {
  return userInfo.value.phone ? formatPhoneNumber(userInfo.value.phone) : "Not Set";
});

const loginPasswordDisplayValue = computed(() => {
  return userInfo.value.login_password ? "Modify" : "Not Set";
});

const paymentPasswordDisplayValue = computed(() => {
  return userInfo.value.withdraw_password ? "Modify" : "Not Set";
});

const isMiniChannel = computed(() => {
  return ["gcash", "maya"].includes(globalStore.channel?.toLowerCase());
});

// ==================== 数据获取 ====================

const init = async () => {
  getKycState();
  getKycData();
  getKycBonus();
};

const getKycData = async () => {
  const res = await getUserKyc({});
  kycData.value = res;
};

const getKycBonus = async () => {
  KycMgr.instance.getKycBonus((responseData) => {
    showKycTip.value = responseData.status == 1 && +responseData.kyc_completed_reward > 0;
    kycBonus.value = responseData;
  });
};

const getKycState = async () => {
  KycMgr.instance.clearData();
  const res = await KycMgr.instance.verifyKyc(InGameType.UNKNOWN);
  kycDetail.value = res;
  //res.state: 状态0未认证 1已认证 2审核中 3拒绝 -1未读服务器
  //res.is_full: 是否是简版本 0是简版 1是完整版 -1未读服务器
};

// ==================== 导航函数 ====================
const jumpKycDetail = () => {
  router.push(`/kyc/detail`);
};

const jumpKycForm = () => {
  if (KycMgr.instance.kycSimple === 0) {
    router.push(`/kyc/simple-form`); // 简版
  } else {
    router.push(`/kyc/normal-form`); // 详版
  }
};

// ==================== 事件处理函数 ====================
const handleKycBtn = () => {
  if (globalStore.channel === CHANEL_TYPE.MAYA) {
    jumpKycDetail();
  } else if (globalStore.channel === CHANEL_TYPE.G_CASH) {
    const firstRestriction = (kycData.value as any)?.first_restriction;
    if (!firstRestriction) {
      jumpKycForm();
    } else {
      jumpKycDetail();
    }
  } else if (globalStore.channel === CHANEL_TYPE.WEB) {
    if (KycMgr.instance.kycState === KycState.NO_VERIFY) {
      KycMgr.instance.inGameType = InGameType.MyCenter;
      KycMgr.instance.checkBindPhone();
    } else if (
      KycMgr.instance.kycState === KycState.COMPLETE ||
      KycMgr.instance.kycState === KycState.REVIEWING
    ) {
      jumpKycDetail();
    } else if (KycMgr.instance.kycState === KycState.REJECTED) {
      jumpKycForm();
    }
  }
};

const handleUpdatePhoneBtn = () => {
  if (userInfo.value.phone) {
    dialogStates.value.showUpdatePhone = true;
  } else {
    dialogStates.value.showBindPhone = true;
  }
};

const handleUpdateLoginPasswordBtn = () => {
  if (!userInfo.value.phone) {
    showToast("For safety of your account, before set login password, you must set phone number.");
    return;
  }
  if (userInfo.value.login_password) {
    dialogStates.value.showUpdateLoginPassword = true;
  } else {
    dialogStates.value.showInitLoginPassword = true;
  }
};

const handleUpdatePaymentPasswordBtn = () => {
  if (userInfo.value.withdraw_password) {
    dialogStates.value.showUpdatePaymentPassword = true;
  } else {
    dialogStates.value.showInitPaymentPassword = true;
  }
};

const handleLogOut = async () => {
  await logout();
  // 重置弹窗状态，准备显示弹窗
  autoPopMgrStore.hasPop = false;
  AutoPopMgr.resetAllPopups();
  globalStore.loginOut();
};

// ==================== 弹窗关闭处理 ====================
const closeDialog = (dialogName: keyof typeof dialogStates.value) => {
  dialogStates.value[dialogName] = false;
};

//
</script>

<template>
  <ZPage
    :request="init"
    :backgroundColor="'transparent'"
    :narBarStyle="{ background: 'transparent' }"
  >
    <div v-if="isMiniChannel">
      <div class="security-center">
        <div class="scroll-content">
          <div class="setting-title">General Setting</div>
          <div class="setting-content">
            <LineSetting
              @click="handleKycBtn"
              text="Personal Details"
              :value="kycStatusConfig.text"
            >
              <template #icon>
                <ZIcon type="icon-kyc" />
              </template>
            </LineSetting>
            <LineSetting
              text="Version No."
              :rightText="ALL_APP_SOURCE_CONFIG.app_version"
              :showArrow="false"
            >
              <template #icon>
                <ZIcon type="icon-version" />
              </template>
            </LineSetting>
          </div>
        </div>
      </div>
    </div>
    <div class="security-center" v-else>
      <div class="scroll-content">
        <div class="setting-title">General Setting</div>
        <div class="setting-content">
          <LineSetting @click="handleUpdatePhoneBtn" :value="phoneDisplayValue" text="Phone">
            <template #icon>
              <ZIcon type="icon-phone" />
            </template>
          </LineSetting>
          <LineSetting
            @click="handleUpdateLoginPasswordBtn"
            :value="loginPasswordDisplayValue"
            text="Login Password"
          >
            <template #icon>
              <ZIcon type="icon-loginpwd" />
            </template>
          </LineSetting>
          <!-- <LineSetting
            @click="handleUpdatePaymentPasswordBtn"
            :value="paymentPasswordDisplayValue"
            text="Payment Password"
          >
            <template #icon>
              <ZIcon class="icon-paypwd" />
            </template>
          </LineSetting> -->
          <LineSetting
            @click="handleKycBtn"
            text="KYC Details"
            :valueStyle="{ color: kycStatusConfig.color }"
            :value="kycStatusConfig.text"
          >
            <template #icon>
              <ZIcon type="icon-kyc" />
            </template>
            <template #rightContent v-if="showKycTip">
              <span class="security-center-kycStatus"
                >Get ₱{{ kycBonus.kyc_completed_reward }} Now!</span
              >
            </template>
          </LineSetting>
        </div>
        <div class="setting-title">Account actions</div>
        <div class="setting-content">
          <LineSetting @click="dialogStates.showLogout = true" text="Logout">
            <template #icon>
              <ZIcon type="icon-Logout" />
            </template>
          </LineSetting>
          <LineSetting
            text="Version No."
            :rightText="ALL_APP_SOURCE_CONFIG.app_version"
            :showArrow="false"
          >
            <template #icon>
              <ZIcon type="icon-version" />
            </template>
          </LineSetting>
        </div>
      </div>
    </div>
    <!-- 更改手机号 -->
    <ZVerifyDialog
      v-model:showDialog="dialogStates.showUpdatePhone"
      :verifyType="PN_VERIFY_TYPE.ChangePhoneNumber"
      :succCallBack="() => closeDialog('showUpdatePhone')"
    />

    <!-- 绑定手机号 -->
    <SetChangePhoneDialog
      v-model:showNextDialog="dialogStates.showBindPhone"
      :verifyType="PN_VERIFY_TYPE.SetPhoneNumber"
      :succCallBack="() => closeDialog('showBindPhone')"
    />

    <!-- 更新登陆密码 -->
    <ZVerifyDialog
      v-model:showDialog="dialogStates.showUpdateLoginPassword"
      :verifyType="PN_VERIFY_TYPE.ForgetPassword"
      :succCallBack="() => closeDialog('showUpdateLoginPassword')"
    />

    <!-- 首次设置登陆密码 -->
    <SetLoginPasswordDialog
      v-model:showNextDialog="dialogStates.showInitLoginPassword"
      :succCallBack="() => closeDialog('showInitLoginPassword')"
    />

    <!-- 更新支付密码 -->
    <ZVerifyDialog
      v-model:showDialog="dialogStates.showUpdatePaymentPassword"
      :verifyType="PN_VERIFY_TYPE.ChangePaymentPassword"
      :succCallBack="() => closeDialog('showUpdatePaymentPassword')"
    />

    <!-- 首次支付密码 -->
    <SetPaymentPasswordDialog
      v-model:showNextDialog="dialogStates.showInitPaymentPassword"
      :verifyType="PN_VERIFY_TYPE.SetPaymentPassword"
      @complete="closeDialog('showInitPaymentPassword')"
    />

    <!-- 退出登陆 -->
    <ZActionSheet
      v-model="dialogStates.showLogout"
      title="Tips"
      :showCancelButton="false"
      confirmText="Log Out"
      :onConfirm="handleLogOut"
    >
      Confirm to log out?
    </ZActionSheet>
  </ZPage>
</template>

<style scoped lang="scss">
.security-center {
  height: 100%;
  background-color: #f4f8fb;

  .icon-version {
    display: inline-block;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    background-color: #97acff1a;
  }

  .scroll-content {
    color: rgba(48, 48, 48, 1);
    // --优化 高度
    overflow-y: auto;

    .setting-title {
      font-size: 14px;
      color: #999;
      padding: 10px 0 6px 10px;
      color: #999;
      font-family: Inter;
    }

    .setting-content {
      background-color: #fff;
      margin: 10px 16px;
      box-sizing: border-box;
      padding: 8px;
      border-radius: 10px;

      .iconfont {
        font-size: 20px;
      }
    }
  }
}

.security-center-kycStatus {
  font-family: "Inter";
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0px;
  text-align: center;
  color: #ac1140;
  margin-left: 30px;
  white-space: nowrap;
}
</style>
