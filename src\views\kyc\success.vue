<template>
  <ZPage>
    <div class="wrap">
      <div class="image">
        <img src="@/assets/images/account/kyc-hua.png" class="hua" />
        <div class="bg">
          <img src="@/assets/images/account/kyc-zan.png" class="zan" />
        </div>
      </div>
      <div class="text">
        <p>Submitted</p>
        <p>Successfully</p>
      </div>
      <!-- 提交按钮 -->
      <div class="footer">
        <div class="submit-btn">
          <ZButton type="primary" :click="handleButtonClick">
            {{ buttonText }}
          </ZButton>
        </div>
      </div>
    </div>
  </ZPage>

</template>
<script lang="ts" setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";


const route = useRoute()
const router = useRouter()
const autoPopMgrStore = useAutoPopMgrStore();


// 检查是否有 simple 参数
const isSimpleMode = computed(() => {
  return route.query.simple !== undefined
})

// 根据模式决定按钮文案
const buttonText = computed(() => {
  return isSimpleMode.value ? 'Close' : 'Close'
})

// 按钮点击处理
const handleButtonClick = () => {
  if (isSimpleMode.value) {
    // simple 模式：返回上一页
    router.back()
  } else {
    autoPopMgrStore.hasPop = false;
    // 普通模式：跳转到首页
    router.replace('/')
  }
}
</script>
<style lang="scss" scoped>
.footer {
  width: 100%;
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    padding: 20px;
    width: 100%;
  }
}

.wrap {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-top: 100px;
  background-color: #fff;
  height: 100%;
}

.text {
  margin-top: 50px;

  >p {
    color: #01D46A;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: "Arial Black";
    font-size: 30px;
    font-style: normal;
    font-weight: 900;
    line-height: 40px;
    /* 133.333% */
  }
}

.image {
  width: 266px;
  height: 188px;
  position: relative;


  .hua {
    width: 266px;
    height: 188px;
    flex-shrink: 0;
  }

  .bg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 140px;
    height: 140px;
    flex-shrink: 0;
    background-color: #FAF5FF;
    border-radius: 50%;
    z-index: 2;
  }

  .zan {
    position: absolute;
    left: 50%;
    top: -20px;
    transform: translateX(-50%);
    width: 114px;
    height: 136px;
    flex-shrink: 0;
  }
}
</style>
