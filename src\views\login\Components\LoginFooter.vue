<template>
  <div class="login-footer">
    <!-- 服务商 Logo -->
    <ProviderLogo class="provider-logos"></ProviderLogo>

    <!-- 隐私协议复选框 -->
    <div v-if="!loginStore.isCodeInputMode" class="agreement-section">
      <van-checkbox
        class="checkbox"
        :model-value="loginStore.isPrivacyAgreed"
        icon-size="16px"
        checked-color="#ac1140"
        label-disabled
        @update:model-value="handleAgreementChange"
      />
      <div class="agreement-text">
        By logging in or registering, you confirm that you are over 21years old, not listed in the
        PAGCOR NDRP, not a GEL licenseholder, and not a government employee. You also agree withthe
        <span class="link-text" @click="handleNavigate('/protocal/terms-of-use')"
          >Terms of use</span
        >
        and
        <span class="link-text" @click="handleNavigate('/protocal/privacy-policy')"
          >Privacy Policy.</span
        >
      </div>
    </div>

    <!-- 版本信息 -->
    <div class="version-info">Version No.{{ appVersion }}</div>
  </div>
</template>

<script setup lang="ts">
/**
 * 登录页面底部组件
 */
import { ALL_APP_SOURCE_CONFIG } from "@/utils/config/Config";
import ProviderLogo from "@/components/Providerlogo.vue";
import { useLoginStore } from "@/stores/login";
import { useRouter } from "vue-router";

const router = useRouter();
const loginStore = useLoginStore();

/** 应用版本号 */
const appVersion = ALL_APP_SOURCE_CONFIG.app_version;

/**
 * 处理隐私协议状态变化
 */
const handleAgreementChange = (accepted: boolean) => {
  loginStore.isPrivacyAgreed = accepted;
};

/**
 * 处理页面导航
 */
const handleNavigate = (path: string) => {
  router.push(path);
};
</script>

<style scoped lang="scss">
.login-footer {
  width: 100%;
  background-color: #fff;
  padding: 20px;
  flex-shrink: 0;
  margin-top: auto;

  .provider-logos {
    width: 100%;
    margin: 20px auto;
  }

  .agreement-section {
    width: 100%;
    position: relative;

    .checkbox {
      position: absolute;
      left: 0;
      top: 4px;
    }

    .agreement-text {
      color: #999;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      text-indent: 20px;
      line-height: 24px;
      /* 200% */

      .link-text {
        color: #222;
        font-family: Inter;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }

  .version-info {
    color: #999;
    text-align: center;
    font-family: Inter;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-top: 10px;
  }
}
</style>
