# Promos 促销活动系统

## 概述

本项目是一个完整的促销活动排行榜系统，经过全面重构和优化，提供了高度可复用、可配置的组件架构。支持多种促销活动类型，具备完整的主题系统和配置管理。

## 项目结构

```
src/views/promos/
├── promos-detail/           # 具体促销页面
│   ├── promo_5.vue         # Casino 排行榜
│   ├── promo_7.vue         # Jili 排行榜
│   └── ...
├── components/              # 可复用组件
│   ├── RankingLayout.vue   # 排行榜布局组件
│   ├── RankingTabs.vue     # 标签页组件
│   ├── RankingTable.vue    # 表格组件
│   ├── RankingPodium.vue   # 颁奖台组件
│   └── RankingFooter.vue   # 底部栏组件
├── composables/             # 组合式函数
│   └── useRankingData.ts   # 排行榜数据管理
├── utils/                   # 工具函数
│   └── promoUtils.ts       # 促销活动专用工具函数
├── configs.ts              # 统一配置文件
└── README.md               # 项目文档
```

## 核心功能

### 1. 统一配置系统 (`configs.ts`)

#### 主题配置

- `PROMO5_THEME` - Casino 主题 (紫色系)
- `PROMO7_THEME` - Jili 主题 (橙色系)
- `DEFAULT_THEME` - 默认主题
- 支持 CSS 变量自动生成

#### 促销配置

- `PROMO5_CONFIG` - Casino 排行榜完整配置
- `PROMO7_CONFIG` - Jili 排行榜完整配置
- 支持 API 函数、主题、组件配置等

#### 类型定义

- `ThemeConfig` - 主题配置接口
- `PromoConfig` - 促销配置接口
- `Tournament` - 锦标赛相关类型
- 完整的 TypeScript 类型支持

### 2. 可复用组件系统

#### RankingLayout (`components/RankingLayout.vue`)

- 完整的排行榜布局组件
- 基于 configId 的简化配置模式
- 集成所有子组件和功能

#### RankingTabs (`components/RankingTabs.vue`)

- 可配置的标签页组件
- 支持主题定制
- 响应式设计

#### RankingTable (`components/RankingTable.vue`)

- van-row/van-col 栅格布局
- 排名图标系统
- 当前用户高亮
- 主题化颜色支持

#### RankingPodium (`components/RankingPodium.vue`)

- 前三名颁奖台展示
- 自动数据格式化
- 用户 ID 脱敏显示

#### RankingFooter (`components/RankingFooter.vue`)

- 用户排名底部栏
- 主题样式支持
- 数据格式化

### 3. 数据管理系统

#### 排行榜数据管理 (`composables/useRankingData.ts`)

- 统一的数据状态管理
- 自动刷新功能
- 错误处理和加载状态
- 批量 API 调用支持

#### 促销工具函数 (`utils/promoUtils.ts`)

**API 辅助工具:**

- `createApiState()` - API 状态管理
- `executeApi()` - 单个 API 调用
- `executeBatchApi()` - 批量 API 调用
- `executeApiWithRetry()` - 重试机制
- `debounceApi()` - 防抖处理
- `cacheApi()` - 结果缓存

**格式化工具:**

- `formatAward()` - 奖励金额格式化
- `formatBetAmount()` - 投注金额格式化
- `safeGetRankData()` - 安全数据获取

**锦标赛工具:**

- `formatTimeRange()` - 时间范围格式化
- `getTournamentStatus()` - 获取锦标赛状态
- `formatPrize()` - 奖金格式化
- `processTournamentData()` - 处理锦标赛数据
- `processTournamentList()` - 批量处理锦标赛列表

**注意:** 通用格式化函数（如数字千分位、货币格式化等）已迁移到 `@/utils/core/tools.ts`

## 技术特性

### 1. 配置驱动架构

- 基于 `configId` 的简化配置模式
- 统一的配置文件管理 (`configs.ts`)
- 主题与功能配置分离

### 2. 组件化设计

- 高度可复用的组件系统
- van-row/van-col 栅格布局
- 主题化样式支持

### 3. 类型安全

- 完整的 TypeScript 类型定义
- 接口约束和类型检查
- 编译时错误检测

### 4. 性能优化

- API 调用缓存和防抖
- 批量数据处理
- 自动刷新机制

## 使用方式

### 基本使用 (推荐)

使用预定义的配置 ID，这是最简单和推荐的方式：

```vue
<template>
  <RankingLayout config-id="promo5" @help-click="handleHelpClick" @tab-change="handleTabChange" />
</template>

<script setup>
import { useRouter } from "vue-router";
import RankingLayout from "@/views/promos/components/RankingLayout.vue";

const router = useRouter();

const handleHelpClick = () => {
  router.push("/promos/promo5_tip");
};

const handleTabChange = (tab) => {
  console.log("Tab changed to:", tab);
};
</script>
```

### 支持的配置 ID

- `"promo5"` - Casino 排行榜 (紫色主题)
- `"promo7"` - Jili 排行榜 (橙色主题)
- `"casino"` - Casino 排行榜 (别名)
- `"jili"` - Jili 排行榜 (别名)

### 配置文件导入

```typescript
import {
  getPromoConfig,
  getTheme,
  PROMO5_CONFIG,
  PROMO7_CONFIG,
  ThemeConfig,
  PromoConfig,
  generateCSSVariables,
} from "@/views/promos/configs";

// 获取促销配置
const config = getPromoConfig("promo5");

// 获取主题配置
const theme = getTheme("casino");

// 生成 CSS 变量
const cssVars = generateCSSVariables(theme);
```

### 组件单独使用

```vue
<template>
  <!-- 单独使用标签页 -->
  <RankingTabs v-model="activeTab" :tabs="tabs" :theme="theme" />

  <!-- 单独使用表格 -->
  <RankingTable :ranking-list="rankingData" :theme="theme" />

  <!-- 单独使用颁奖台 -->
  <RankingPodium :top-three-data="topThreeData" bet-label="Bet" />
</template>
```

## 优化成果

### 代码大幅简化

- `promo_5.vue`: 从 427 行减少到 **17 行** (-96%)
- `promo_7.vue`: 从 432 行减少到 **17 行** (-96%)
- 总体减少约 **800+ 行代码**

### 架构优化

- **配置统一**: 三个目录合并为单一配置文件 (`configs.ts`)
- **组件复用**: 5 个可复用组件替代重复代码
- **类型安全**: 完整的 TypeScript 类型系统
- **主题系统**: 可配置的主题和样式

### 开发体验提升

- **简化使用**: 仅需 `config-id` 一个参数
- **快速开发**: 新增促销活动只需配置
- **易于维护**: 集中的配置管理
- **类型提示**: 完整的 IDE 支持

### 性能优化

- **API 优化**: 缓存、防抖、重试机制
- **布局优化**: 栅格系统和响应式设计
- **加载优化**: 批量数据处理和状态管理

## 扩展指南

### 添加新的促销活动

1. 在 `configs.ts` 中定义新配置：

```typescript
export const PROMO8_CONFIG: PromoConfig = {
  id: "promo8",
  name: "New Promo",
  theme: CUSTOM_THEME,
  apiFunction: newPromoApi,
  tabs: [
    { label: "Today", value: "1" },
    { label: "Yesterday", value: "2" },
  ],
  tableColumns: [
    { key: "rank", label: "Rank" },
    { key: "user", label: "User ID" },
    { key: "bet", label: "Bet" },
    { key: "bonus", label: "Bonus" },
  ],
  infoBannerImage: labaImg,
  helpRoute: "/promos/promo8_tip",
  showPodium: true,
  showInfoBanner: true,
  betLabel: "Bet",
  userLabel: "Me",
  autoRefresh: true,
  refreshInterval: 30000,
};

// 添加到配置映射
export const PROMO_CONFIGS = {
  // ... 现有配置
  promo8: PROMO8_CONFIG,
} as const;
```

2. 创建新页面：

```vue
<template>
  <RankingLayout config-id="promo8" @help-click="handleHelp" />
</template>
```

### 自定义主题

```typescript
export const CUSTOM_THEME: ThemeConfig = {
  name: "custom",
  colors: {
    primary: "#your-primary-color",
    secondary: "#your-secondary-color",
    background: "#your-background",
    gradient: "linear-gradient(180deg, #start 0%, #end 100%)",
    tabBackground: "#your-tab-bg",
    tabActive: "#your-tab-active",
    tabActiveText: "#your-tab-text",
    tableBackground: "#your-table-bg",
    tableRowBackground: "#your-row-bg",
    rankNumber: "#your-rank-color",
    rankBonus: "#your-bonus-color",
    footerBackground: "#your-footer-bg",
    infoBackground: "#your-info-bg",
  },
  backgroundImage: "url(your-background-image)",
  backgroundPosition: "center",
};
```

## 技术栈

### 核心技术

- **Vue 3** - 组合式 API
- **TypeScript** - 类型安全
- **Vant UI** - 移动端组件库
- **SCSS** - 样式预处理

### 设计模式

- **配置驱动** - 基于配置的组件行为
- **组合式架构** - 可复用的组件系统
- **主题化设计** - 可配置的视觉样式
- **类型约束** - 编译时错误检测

## 注意事项

1. **依赖要求**:

   - Vant UI 组件库
   - D-DIN 和 Inter 字体
   - 相关图标和图片资源

2. **配置管理**:

   - 所有配置集中在 `configs.ts`
   - 主题和功能配置分离
   - 类型定义完整

3. **组件使用**:
   - 推荐使用 `config-id` 模式
   - 支持组件单独使用
   - 遵循响应式设计原则

## 总结

这个促销活动系统通过全面的架构重构，实现了：

- **极简使用**: 一个参数完成配置
- **高度复用**: 组件可在多个场景使用
- **类型安全**: 完整的 TypeScript 支持
- **易于扩展**: 新增功能只需配置
- **性能优化**: 多种优化策略集成

系统设计遵循了现代前端开发的最佳实践，为促销活动的快速开发和维护提供了强有力的支持。
