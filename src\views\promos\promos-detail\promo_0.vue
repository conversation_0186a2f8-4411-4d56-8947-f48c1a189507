<template>
  <XPage :navTitle="pageDetails.title" :loading="loading">
    <template #left-icon>
      <ZIcon type="icon-fanhui" color="#333" />
    </template>
    <!-- 通用活动详情页面，可配置内容 -->
    <div class="promo-detail">
      <div class="promo-title" v-if="pageDetails.picture_details_title">
        <ZImage
          :src="getServerSideImageUrl(pageDetails.picture_details_title)"
          alt="title"
          class="title-img"
        />
      </div>
      <div
        class="promo-content"
        :style="{
          'padding-bottom': pageDetails.button_text ? '120px' : 0 /* 给底部按钮留空间 */,
        }"
      >
        <ZImage
          v-for="(img, idx) in pageDetails.picture_details"
          :key="idx"
          :src="getServerSideImageUrl(img)"
          alt="detail"
          class="detail-img"
        />
      </div>
      <div class="promo-btn-wrap" v-show="pageDetails.button_text">
        <button class="promo-btn" @click="handleBtnClick">{{ pageDetails.button_text }}</button>
      </div>
      <ZActionSheet
        v-model="visible"
        title="Games"
        :showCancelButton="false"
        :showConfirmButton="false"
        :overlay="false"
      >
        <van-row>
          <van-col v-for="row in gameList" :key="row.id" :span="row.span">
            <div class="game-div">
              <ZImage :src="row.images" alt="game" class="game-img" />
              <ZButton @click="() => jumpTo(row)" class="promo-foot-button">Bet Now</ZButton>
            </div>
          </van-col>
        </van-row>
      </ZActionSheet>
    </div>
  </XPage>
</template>

<script setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { jumpGame } from "@/utils/JumpGame";
import { getGameInfo } from "@/api/games";
import { useDepositStore } from "@/stores/deposit";
import { getServerSideImageUrl } from "@/utils/core/tools";
import { MobileWindowManager } from "@/utils/managers/MobileWindowManager";

const depositStore = useDepositStore();
const router = useRouter();
const route = useRoute();

const loading = ref(true);
const visible = ref(false); // 控制 ActionSheet 的显示状态
const gameList = ref([]); // 用于存储游戏列表
const pageDetails = ref(route.query.detail ? JSON.parse(route.query.detail) : {});

const jumpTo = async (item) => {
  jumpGame(item);
};

async function handleBtnClick() {
  // 1. 跳转到游戏列表页面
  if (pageDetails.value.pictures_jump === 1) {
    router.replace(`/game-categories?category=${pageDetails.value.game_type}`);
    return;
  }
  // 2. 展示游戏列表面板
  if (pageDetails.value.pictures_jump === 2) {
    visible.value = true; // 显示 ActionSheet
    return;
  }
  // 3. 打开充值面板
  if (pageDetails.value.pictures_jump === 3) {
    depositStore.openDialog();
    return;
  }
  // 4. 内跳  跳转到指定的路由
  if (pageDetails.value.pictures_jump === 4) {
    // 这里可以根据实际需求跳转到具体的页面
    router.push({ path: "/promo-webview", query: { url: pageDetails.value.url } });
    return;
  }
  // 5. 外跳：跳转到指定的 URL
  if (pageDetails.value.pictures_jump === 5) {
    try {
      await MobileWindowManager.launchExternalGame(
        async () => pageDetails.value.url,
        (error) => {
          console.error("Failed to open promo URL:", pageDetails.value.url, error);
        }
      );
    } catch (error) {
      console.error("Promo URL error:", error);
    }
  }
}

const getGameList = async () => {
  if (pageDetails.value.pictures_jump === 2) {
    const ids = pageDetails.value.gid.split(",");
    const remNum = ids.length % 3; // 计算余数
    const posNum = ids.length - remNum; // 计算需要填充的数量
    const allRes = [];
    ids.map((id, index) => {
      // 调用 API 获取游戏详情
      const res = getGameInfo({ id });
      allRes.push(res);
    });
    const resList = await Promise.all(allRes);
    const rowDatas = {}; // 按照行号存储游戏数据
    // 批量处理返回Promise结果
    resList.forEach((item, index) => {
      const rowNum = Math.floor(index / 3); // 计算当前行号
      if (!rowDatas[rowNum]) {
        rowDatas[rowNum] = [];
      }
      // 根据游戏数量动态设置每行显示的游戏数量
      rowDatas[rowNum].push({ ...item[0], span: posNum > index ? 8 : 24 / remNum });
    });
    // 按照行号按照从下倒上存储
    Object.values(rowDatas).forEach((row) => {
      gameList.value.unshift(...row);
    });
    visible.value = gameList.value.length > 0; // 初始化默认显示 ActionSheet
  }
};

onBeforeMount(() => {
  // 根据页面详情数据判断是否需要获取游戏列表
  if (pageDetails.value.pictures_jump === 2) {
    getGameList();
  }
});

onMounted(() => {
  nextTick(() => {
    loading.value = false;
  });
});
</script>

<style lang="scss" scoped>
.icon-fanhui {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgb(238, 238, 238, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.game-div {
  gap: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 16px;

  .game-img {
    width: 100px;
    height: 100px;
    border-radius: 12px;
  }

  .promo-foot-button {
    width: 100px;
    font-size: 14px;
    height: 30px;
  }
}

.promo-detail {
  display: flex;
  flex-direction: column;
  // max-height: 100vh;
  background: #fff;
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;

  .promo-title,
  .detail-img {
    width: 100%;
    height: auto;

    img {
      width: 100%;
      height: auto;
    }
  }
}

.promo-title {
  height: 100px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    height: 100px;
    width: 100%;
  }
}

.promo-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    height: 100px;
    width: 100%;
  }
}

.promo-btn-wrap {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 10px;
  background: transparent;
  display: flex;
  justify-content: center;
  pointer-events: none;
  z-index: 10;
}

.promo-btn {
  width: 90%;
  padding: 8px 12px;
  font-size: 16px;
  color: #fff;
  background: #9b113b;
  border: none;
  border-radius: 48px;
  font-weight: bold;
  box-shadow: 0 4px 24px rgba(140, 34, 50, 0.18);
  pointer-events: auto;
  transition: background 0.2s;
}

.promo-btn:active {
  background: #a63a4b;
}
</style>
