<template>
  <XPage :navBarStyle="{ backgroundColor: '#FEA100', color: '#333' }">
    <template #left-icon>
      <ZIcon type="icon-fanhui" />
    </template>
    <div class="promo-container">
      <div class="promo12-content">
        <h1 class="title">GENERAL MECHANICS</h1>
        <ol class="rule-list">
          <li class="rule-item">This promotion is open to both new and existing NUSTAR members!</li>
          <li class="rule-item">
            Meet the betting requirements for each game Super Bang Bang, Lucky Fortunes, and Golden
            Genie to unlock up to 140 Free Spins for each game with bonus up to ₱1,000,000.
            <div class="game-buttons">
              <div class="game-item">
                <img
                  src="@/assets/images/promos/promo16-1.png"
                  alt="Super Bang Bang"
                  class="game-img"
                />
                <button class="bet-now">BET NOW</button>
              </div>
              <div class="game-item">
                <img
                  src="@/assets/images/promos/promo12-2.png"
                  alt="Lucky Fortunes"
                  class="game-img"
                />
                <button class="bet-now">BET NOW</button>
              </div>
              <div class="game-item">
                <img
                  src="@/assets/images/promos/promo12-3.png"
                  alt="Golden Genie"
                  class="game-img"
                />
                <button class="bet-now">BET NOW</button>
              </div>
            </div>
          </li>
          <li class="rule-item">
            Bet amounts for Super Bang Bang, Lucky Fortunes, and Golden Genie are calculated
            separately. Reach the required bet amount and instantly claim your free spins!
          </li>
        </ol>
        <div class="table-container">
          <van-row class="table-head">
            <van-col span="12">BET AMOUNT</van-col>
            <van-col span="12">FREE SPIN</van-col>
          </van-row>
          <van-row
            v-for="(row, index) in betList"
            :key="row.bet"
            :style="{ background: (index + 1) % 2 > 0 ? '#FFD58B' : '#f2c172' }"
          >
            <van-col span="12">{{ row.bet }}</van-col>
            <van-col span="12">{{ row.spin }}</van-col>
          </van-row>
        </div>
        <ol class="rule-list" start="3">
          <li class="rule-item">
            To ensure a fair gaming experience, you need to exit and re-enter the game after
            reaching the required betting amount for the system to automatically issue your Free
            Spins.
          </li>
          <li class="rule-item">
            This promotion follows a natural weekly cycle, running from Monday at 00:00 to Sunday at
            23:00. All accumulated bets and spins will be reset every Monday.
          </li>
          <li class="rule-item">No wagering requirements are needed to withdraw your winnings.</li>
        </ol>
      </div>
      <ZFootPng background="linear-gradient(90deg,#fece9d,#ff8003)" />
    </div>
    <!-- <img src="@/assets/images/promos/promo_12.png" alt="" /> -->
    <!-- <ZFootPng background="linear-gradient(90deg,#fece9d,#ff8003)" /> -->
  </XPage>
</template>

<script setup>
const betList = [
  { bet: "1,000", spin: "5" },
  { bet: "3,000", spin: "10" },
  { bet: "5,000", spin: "25" },
  { bet: "10,000", spin: "40" },
  { bet: "50,000", spin: "60" },
];
</script>

<style lang="scss" scoped>
.icon-fanhui {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgb(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.promo12-content {
  font-family: "Inter", "D-DIN";
  padding: 50px 20px 0;
  font-size: 12px;
  font-weight: 500;
}
.promo-container {
  background: linear-gradient(180deg, #ffa90d, #ffa925, #ffac2a);
  margin: 0 auto;
  color: #000;
}
.title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 10px;
}
.rule-list {
  list-style: decimal;
  margin-left: 20px;
  margin-bottom: 10px;
  counter-reset: my-counter;
  li {
    counter-increment: my-counter;
  }
}
.rule-item {
  margin-bottom: 10px;
  line-height: 1.6;
}
.game-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 10px auto;
}
.game-item {
  text-align: center;
}
.game-img {
  width: 60px;
  height: 60px;
  margin-bottom: 5px;
  border-radius: 12px;
  border: 1px solid #fff;
  overflow: hidden;
}
.bet-now {
  font-size: 10px;
  padding: 4px 8px;
  background: linear-gradient(180deg, #ee5a00 0%, #cc1000 80%);
  box-shadow: -2px 2px 2px rgba(0, 0, 0, 0.3);
  color: #fff;
  border-radius: 4px;
}

.table-container {
  margin: 10px 0;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 34px;
  background: #ffd58b;
  border: none;
  color: #333;
  border-radius: 16px;
  overflow: hidden;
  .table-head {
    color: #fff;
    background: #ec7f1f;
    font-weight: 400;
    line-height: 1.6;
    font-size: 14px;
    &:deep(.van-col) {
      padding: 8px 0px;
    }
  }

  &:deep(.van-col) {
    display: flex;
    align-items: center;
    justify-content: center;
    &:first-child {
      border-right: 1px solid #c79952;
    }
  }
}
</style>
