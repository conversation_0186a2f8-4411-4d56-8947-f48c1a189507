<template>
  <ZPage :request="initData" backgroundColor="#f5f7fa" :narBarStyle="narBarStyle" :onBack="handleBack">
    <div class="bonus-wallet-container">
      <!-- 余额头部 -->
      <div class="balance-header">
        <Balance ref="balanceRef" :coinSize="32" :balanceSize="18" />
        <div class="wallet-icon" @click="handleShowRules">
          <img src="@/assets/images/reward-wallet/rule-desc.png" alt="规则" />
        </div>
      </div>

      <!-- 奖金列表容器 -->
      <div class="bonus-list-wrap">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <ZLoading />
        </div>

        <!-- 空状态 -->
        <div v-else-if="bonusList.length === 0" class="empty-state">
          <img src="@/assets/images/reward-wallet/no-data.png" alt="No data" />
        </div>

        <!-- 奖金列表 -->
        <div v-else class="bonus-list">
          <van-pull-refresh v-model="refreshing" :disabled="loading" loading-text="loading..."
            loosing-text="Release to refresh" pulling-text="Pulling to refresh" success-text="Refresh successful"
            @refresh="handleRefresh">
            <BonusItem v-for="bonus in bonusList" :key="bonus.id" :bonus="bonus"
              :has-ongoing-bonuses="hasOngoingBonuses"
              :is-highlighted="showGuideDialog && bonus.task_status === TaskStatus.COMPLETED" @claim="clickClaimBonus"
              @unlock="handleReceiveWalletTask" @ongoing="jumpToHistory" ref="bonusItemRefs" />
          </van-pull-refresh>
        </div>
      </div>

      <!-- 底部内容 -->
      <div class="bottom-content">
        <!-- 底部推广横幅 -->
        <div class="promo-banner">
          <Carousel bannerType="rewardWallet" />
        </div>
        <!-- 查看更多奖金按钮 -->
        <ZButton class="view-more-btn" @click="handleViewMoreBonuses">
          View More Bonuses
        </ZButton>
      </div>
    </div>

    <!-- 规则弹窗 -->
    <ZActionSheet v-model="showRules" title="Rule" :closeOnClickOverlay="false" :showConfirmButton="false"
      :showCancelButton="false">
      <div class="rules-list">
        <div v-for="(rule, index) in rules" :key="rule">{{ index + 1 }}. {{ rule }}</div>
      </div>
    </ZActionSheet>

    <!-- 引导弹窗 -->
    <BonusGuideDialog v-model:show="showGuideDialog" :hasNoData="!loading && bonusList.length === 0"
      @confirm="handleGuideConfirm" />

    <!-- 引入金币动画组件 -->
    <CoinAnimation ref="coinAnimationRef" :start-ref="currentLaunchRef" :end-ref="balanceRef?.coinRef"
      @animation-end="handleAnimationEnd" @animation-start="handleAnimationStart" />
  </ZPage>
</template>

<script setup lang="ts">
defineOptions({ name: "RewardWallet" });

// Vue 相关导入
import { ref, onUnmounted, onActivated, onBeforeUnmount } from "vue";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";

// Store 导入
import { useGlobalStore } from "@/stores/global";
import { useDepositStore } from "@/stores/deposit";
import { useRewardWalletStore, TaskStatus, type WalletTaskItem } from "@/stores/rewardWallet";
import { useConfigurableCountdown, TimeUnit } from "@/composables/useConfigurableCountdown";

// 工具函数导入
import { throttleFirst } from "@/utils/core/tools";
import { bonusWalletManager } from "@/utils/managers/BonusWalletManager";

// 组件导入
import Carousel from "@/views/home/<USER>/Carousel.vue";
import BonusItem from "./Components/BonusItem.vue";
import BonusGuideDialog from "./components/BonusGuideDialog.vue";
import CoinAnimation from "@/components/CoinAnimation/index.vue";
import { showToast } from "vant";

// 金币动画模板引用
const coinAnimationRef = ref<any>(null); // 金币组件引用
const balanceRef = ref<any>(null); // 钱包金币 组件引用
const bonusItemRefs = ref<any[]>([]); // BonusItem 组件引用数组
const currentLaunchRef = ref<any>(null); // 当前发射位置引用

// Store 实例和状态管理
const router = useRouter();
const globalStore = useGlobalStore();
const depositStore = useDepositStore();
const rewardWalletStore = useRewardWalletStore();

const { balance, userInfo } = storeToRefs(globalStore);
const userId = userInfo.value?.user_id || "99";

// 从store获取状态
const { loading, bonusList, rules, availableCount, showGuideDialog, hasOngoingBonuses } =
  storeToRefs(rewardWalletStore);

// 本地状态
const showRules = ref(false);
const refreshing = ref(false); // 下拉刷新状态

// 创建倒计时实例
const { startCountdown, stopCountdown } = useConfigurableCountdown(bonusList);

// 页面配置
const narBarStyle = {
  backgroundColor: "#f5f7fa",
  color: "#000",
};

// 数据初始化
const initData = async () => {
  try {
    await rewardWalletStore.initData(userId);
    // 数据加载完成后，保存当前奖金内容状态
    bonusWalletManager.saveCurrentBonusContent(bonusList.value, availableCount.value, userId);
    console.log("RewardWallet: 数据初始化完成，保存当前奖金内容");
    // 开始倒计时 先清理后开始
    stopCountdown();
    startCountdown();
  } catch (error) {
    console.error("初始化数据失败:", error);
  }
};

// 启动动画
const hanldeLaunch = (launchElement?: any) => {
  // 如果传入了特定的发射元素，使用它；否则使用当前设置的引用
  if (launchElement) {
    currentLaunchRef.value = launchElement;
  }

  if (coinAnimationRef.value && currentLaunchRef.value) {
    coinAnimationRef.value.startAnimation();
  }
};

const handleAnimationStart = () => {
  balanceRef.value?.startAnimation();
};

// 动画结束回调
const handleAnimationEnd = () => {
  balanceRef.value?.stopAnimation();
};

// 事件处理函数
const handleRefresh = async () => {
  try {
    await rewardWalletStore.initData(userId);
    // 刷新完成后，保存当前奖金内容状态
    bonusWalletManager.saveCurrentBonusContent(bonusList.value, availableCount.value, userId);
    console.log("RewardWallet: 刷新完成，保存当前奖金内容");

    // 重新启动倒计时
    stopCountdown();
    startCountdown();
  } catch (error) {
    console.error("刷新失败:", error);
  } finally {
    refreshing.value = false;
  }
};
// 领取任务
const handleReceiveWalletTask = throttleFirst(async (bonus: WalletTaskItem, event?: Event) => {
  try {
    await rewardWalletStore.receiveTask(bonus);
  } catch (error) {
    console.error("解锁任务失败:", error);
  }
}, 2000);
// 领取奖励
const clickClaimBonus = throttleFirst(async (bonus: WalletTaskItem, event?: Event) => {
  try {
    // 找到对应的 BonusItem 组件
    const bonusItem = bonusItemRefs.value.find((item) => item.bonus?.id === bonus.id);

    // 如果找到了组件，获取其 launchRef
    if (bonusItem && bonusItem.launchRef) {
      currentLaunchRef.value = bonusItem.launchRef;
    } else if (event && event.target) {
      // 如果没有找到组件但有事件对象，使用事件目标作为发射位置
      currentLaunchRef.value = event.target;
    }

    const res = await rewardWalletStore.claimReward(bonus);
    if (res) {
      hanldeLaunch();
      // router.push("/account/transactions/Reward");
    }
  } catch (error) {
    console.error("领取奖金失败:", error);
  }
}, 2000);

// 导航处理
const jumpToHistory = () => {
  depositStore.clickBetNow();
};

const handleViewMoreBonuses = () => {
  router.push("/promos/0");
};

// UI 交互处理
const handleGuideConfirm = () => {
  rewardWalletStore.confirmGuide();
};

const handleShowRules = () => {
  if (rules.value.length > 0) {
    showRules.value = true;
  } else {
    showToast("No rules available.");
  }
};

// 保存用户访问状态的通用方法
const saveUserVisitStatus = () => {
  bonusWalletManager.markUserVisitedBonusWallet(userId);
  bonusWalletManager.saveCurrentBonusContent(bonusList.value, availableCount.value, userId);
  console.log("RewardWallet: 已保存用户访问状态和奖金内容");
};

// 页面返回处理
const handleBack = () => {
  saveUserVisitStatus();
  console.log("RewardWallet: 用户点击返回按钮");
  router.back();
};

// 实时更新bonusList的方法
const updateBonusListRealtime = async () => {
  try {
    await rewardWalletStore.fetchBonusList(userId);
    // 更新完成后，保存当前奖金内容状态
    bonusWalletManager.saveCurrentBonusContent(bonusList.value, availableCount.value, userId);
  } catch (error) {
    console.error("实时更新bonusList失败:", error);
  }
};

// 生命周期管理
onActivated(async () => {
  // 页面激活时标记用户已访问（处理通过路由直接访问的情况）
  console.log("RewardWallet: 页面激活，标记用户已访问");
  bonusWalletManager.markUserVisitedBonusWallet(userId);

  // 实时更新bonusList内容
  await updateBonusListRealtime();

  // 如果数据已经加载完成，立即保存当前状态
  if (!loading.value && bonusList.value.length >= 0) {
    bonusWalletManager.saveCurrentBonusContent(bonusList.value, availableCount.value, userId);
    console.log("RewardWallet: 页面激活时保存当前奖金内容");
  }
});

onBeforeUnmount(() => {
  // 页面卸载前保存状态（处理用户通过其他方式离开页面的情况）
  saveUserVisitStatus();
  console.log("RewardWallet: 页面即将卸载，保存状态");
});

onUnmounted(() => {
  stopCountdown();
});
</script>

<style scoped lang="scss">
// 主容器
.bonus-wallet-container {
  padding: 16px;
  background: #f5f7fa;
  font-family: "Inter";
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 30px;
  padding: 8px 10px;
  margin-bottom: 16px;

  .balance {
    font-family: "D-DIN";
    font-weight: 700;
    font-size: 18px;
    color: #222;
  }

  .balance-wrap {
    background-color: #fff5ed;
    padding-right: 18px;
    border-radius: 20px;
    // gap: 12px;
  }

  .wallet-icon {
    width: 28px;
    height: 28px;
  }
}

// 奖金列表区域
.bonus-list-wrap {
  margin-bottom: 190px;
}

.bonus-list {
  min-height: calc(100vh - 340px);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

// 状态样式

// 加载状态
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  background: #fff;
  border-radius: 16px;

  img {
    width: 164px;
    height: 157px;
  }
}

// 底部内容
.bottom-content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  background: #fff;
  padding: 10px 10px 20px;
  border-radius: 12px 12px 0 0;
}

.promo-banner {
  margin-bottom: 18px;
  overflow: hidden;
  border-radius: 12px;
}
</style>
