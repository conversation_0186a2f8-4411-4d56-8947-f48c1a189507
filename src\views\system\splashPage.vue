<template>
  <div class="splash-page">
    <SplashScreen :external-progress="initProgress" />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useRouter } from "vue-router";
import SplashScreen from "@/components/SplashScreen.vue";
import { loginManager } from "@/utils/managers/LoginManager";
import { appInitializer } from "@/utils/preload/AppInitializer";
import { setAppInitialized } from "@/router/index";
import { useGlobalStore } from "@/stores/global";
import { useGameStore } from "@/stores/game";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { KycMgr, InGameType } from "@/utils/KycMgr";
import { GeetestMgr } from "@/utils/GeetestMgr";
import { useMayaToken } from "@/composables/useMayaToken";

const router = useRouter();
const globalStore = useGlobalStore();
const gameStore = useGameStore();
const autoPopMgrStore = useAutoPopMgrStore();
const { startRefresh } = useMayaToken();

// 应用初始化进度
const initProgress = appInitializer.getProgress();

// 公用前置接口初始化
const initializeApp = async () => {
  try {
    await appInitializer.initialize((progress) => {
      // 更新进度显示，但不要超过 95%，为最终完成留出空间
      initProgress.value = Math.min(progress.value, 95);
    });
    return true;
  } catch (error) {
    console.error("应用初始化失败:", error);
    initProgress.value = 95; // 即使失败也要到 95%
    return false;
  }
};

// 获取目标路由
const getTargetRoute = (): string => {
  // 优先从 sessionStorage 获取保存的目标路由
  const savedRoute = sessionStorage.getItem("targetRoute");
  if (savedRoute && savedRoute !== "/splash") {
    sessionStorage.removeItem("targetRoute"); // 清除保存的路由
    return savedRoute;
  }
  // 默认跳转到首页
  return "/";
};

// 重置应用状态
const resetAppState = () => {
  autoPopMgrStore.hasPop = false;
  KycMgr.instance.clearData();
};

// 执行基础服务初始化
const initializeServices = async (token?: string) => {
  // 启动 Maya token 刷新
  const currentToken = token || globalStore.token;
  if (currentToken) {
    startRefresh();
  }

  const tasks = [
    {
      name: "获取基础配置",
      task: () => gameStore.getConfigData(),
      critical: true,
    },
    {
      name: "获取维护列表",
      task: () => gameStore.getMaintenanceList(true),
      critical: false,
    },
    {
      name: "获取小转盘信息",
      task: () => autoPopMgrStore.getSpinInfo(),
      critical: false,
    },
    {
      name: "初始化 Geetest",
      task: () => GeetestMgr.instance.initializeAfterChannelSet(),
      critical: false,
    },
  ];

  // 并行执行非关键任务，串行执行关键任务
  const results = await Promise.allSettled(
    tasks.map(async ({ name, task, critical }) => {
      try {
        await task();
        return { name, success: true };
      } catch (error) {
        if (critical) {
          throw error; // 关键任务失败时抛出错误
        }
        return { name, success: false, error };
      }
    })
  );
};

// 平滑更新进度到目标值
const smoothUpdateProgress = async (targetProgress: number, duration: number = 300) => {
  const currentProgress = initProgress.value;
  const progressDiff = targetProgress - currentProgress;

  if (progressDiff <= 0) {
    initProgress.value = targetProgress;
    return;
  }

  const steps = Math.max(10, Math.min(30, progressDiff)); // 10-30步之间
  const stepDuration = duration / steps;
  const stepSize = progressDiff / steps;

  for (let i = 0; i < steps; i++) {
    initProgress.value = currentProgress + stepSize * (i + 1);
    await new Promise((resolve) => setTimeout(resolve, stepDuration));
  }

  initProgress.value = targetProgress;
};

// 执行应用初始化
const performInitialization = async () => {
  try {
    // 第一阶段：重置状态 (0-10%)
    resetAppState();
    await smoothUpdateProgress(10);

    // 第二阶段：执行登录逻辑 (10-70%)
    await loginManager.preLogin(async (token) => {
      // 初始化服务
      KycMgr.instance.verifyKyc(InGameType.UNKNOWN);
      await initializeServices(token);
      await smoothUpdateProgress(70);
    });

    // 第三阶段：应用初始化 (70-95%)
    await initializeApp();
    await smoothUpdateProgress(95);
  } catch (error) {
    // 错误恢复：尝试基本的应用初始化
    try {
      await initializeApp();
      await smoothUpdateProgress(95);
    } catch (fallbackError) {
      // 即使失败也要继续，确保应用能启动
      await smoothUpdateProgress(95);
    }
  }

  // 第四阶段：完成初始化 (95-100%)
  await smoothUpdateProgress(100, 500); // 较慢的最终进度

  // 等待用户看到完成状态
  await new Promise((resolve) => setTimeout(resolve, 300));

  // 标记完成并跳转
  await completeInitialization();
};

// 完成初始化并跳转
const completeInitialization = async () => {
  setAppInitialized(true);

  // 获取目标路由并跳转
  const targetRoute = getTargetRoute();

  try {
    await router.replace(targetRoute);
  } catch (error) {
    // 降级处理：跳转到首页
    try {
      await router.replace("/home");
    } catch (fallbackError) {
      // 最后的降级：强制刷新页面
      window.location.href = "/home";
    }
  }
};

onMounted(() => {
  // 设置渠道
  loginManager.setChannel();

  // 重置进度
  initProgress.value = 0;

  // 开始初始化
  performInitialization();
});
</script>

<style scoped lang="scss">
.splash-page {
  width: 100vw;
  height: 100vh;
  position: relative;
}
</style>
